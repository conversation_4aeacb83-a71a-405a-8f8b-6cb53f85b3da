const http = require('http');

// 测试数据
const testData = {
  id: 2001,
  username: "NewPlayer",
  avatar: "https://example.com/new-avatar.jpg",
  coins: 25000
};

// 测试上报玩家数据
function testReportPlayer() {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify(testData);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/players/report',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(data)
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    req.write(data);
    req.end();
  });
}

// 测试获取排行榜
function testGetLeaderboard() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/leaderboard?limit=10',
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    req.end();
  });
}

// 测试手动刷新排行榜
function testRefreshLeaderboard() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/leaderboard/refresh',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    req.end();
  });
}

// 运行测试
async function runTests() {
  console.log('🚀 开始测试 API 接口...\n');

  try {
    // 测试 1: 上报玩家数据
    console.log('📝 测试 1: 上报玩家数据');
    const reportResult = await testReportPlayer();
    console.log(`状态码: ${reportResult.status}`);
    console.log('响应:', JSON.stringify(reportResult.data, null, 2));
    console.log('');

    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 测试 2: 手动刷新排行榜
    console.log('🔄 测试 2: 手动刷新排行榜');
    const refreshResult = await testRefreshLeaderboard();
    console.log(`状态码: ${refreshResult.status}`);
    console.log('响应:', JSON.stringify(refreshResult.data, null, 2));
    console.log('');

    // 等待排行榜生成
    console.log('⏳ 等待排行榜生成...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 测试 3: 获取排行榜
    console.log('📊 测试 3: 获取排行榜');
    const leaderboardResult = await testGetLeaderboard();
    console.log(`状态码: ${leaderboardResult.status}`);
    console.log('响应:', JSON.stringify(leaderboardResult.data, null, 2));
    console.log('');

    console.log('✅ 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

runTests();
