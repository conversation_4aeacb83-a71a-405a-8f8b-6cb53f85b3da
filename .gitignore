# 旧项目
#/old

# 编译输出
/dist

# 忽略根目录下的 node_modules
node_modules/

# 忽略所有子目录下的 node_modules
**/node_modules/

# 日志
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 备份
/backups
*.backup
# 测试
/coverage
/.nyc_output
/test-logs
/test-results
/test-reports

# IDEs 和编辑器
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# 操作系统
.DS_Store
Thumbs.db

# 本地测试数据
/data
/tmp

# 生成的NestJS文件
ormconfig.json
/src/migrations
