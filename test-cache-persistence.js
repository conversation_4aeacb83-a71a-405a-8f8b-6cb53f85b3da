#!/usr/bin/env node

/**
 * 缓存持久化测试脚本
 * 测试缓存是否真正写入Redis并持久化
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/app.module');
const { CACHE_MANAGER } = require('@nestjs/cache-manager');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

async function testCachePersistence() {
  console.log('🔍 开始测试缓存持久化...\n');

  let app;
  try {
    // 创建应用实例
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: ['error'],
    });

    const cacheManager = app.get(CACHE_MANAGER);

    // 测试1: 写入缓存
    console.log('📝 测试1: 写入缓存数据');
    const testKey = 'leaderboard:test-persistence';
    const testData = {
      type: 'test-persistence',
      leaderboard: [
        { rank: 1, id: '9999', username: 'TestUser', value: 100000 }
      ],
      generatedAt: new Date().toISOString(),
      totalPlayers: 1,
      fromCache: false
    };

    await cacheManager.set(testKey, testData, 300000); // 5分钟TTL
    console.log('✅ 缓存数据写入完成');
    console.log('键:', testKey);
    console.log('');

    // 测试2: 立即读取缓存
    console.log('📖 测试2: 立即读取缓存');
    const cachedData = await cacheManager.get(testKey);
    if (cachedData) {
      console.log('✅ 缓存读取成功');
      console.log('数据:', JSON.stringify(cachedData, null, 2));
    } else {
      console.log('❌ 缓存读取失败');
    }
    console.log('');

    // 测试3: 检查Redis中的键
    console.log('🔍 测试3: 检查Redis中的键');
    try {
      const { stdout } = await execAsync('redis-cli -h 192.168.200.200 -p 6379 -a 123456 keys "rankserver:*"');
      console.log('Redis中的rankserver键:');
      if (stdout.trim()) {
        console.log(stdout);
      } else {
        console.log('(没有找到rankserver:*键)');
      }
    } catch (error) {
      console.error('Redis命令执行失败:', error.message);
    }
    console.log('');

    // 测试4: 检查所有可能的键模式
    console.log('🔍 测试4: 检查所有可能的键模式');
    const keyPatterns = [
      '*leaderboard*',
      '*test-persistence*',
      'keyv:*',
      'cache:*',
      'nestjs:*'
    ];

    for (const pattern of keyPatterns) {
      try {
        const { stdout } = await execAsync(`redis-cli -h 192.168.200.200 -p 6379 -a 123456 keys "${pattern}"`);
        console.log(`模式 ${pattern}:`);
        if (stdout.trim()) {
          console.log(stdout);
        } else {
          console.log('(没有找到匹配的键)');
        }
      } catch (error) {
        console.error(`检查模式 ${pattern} 失败:`, error.message);
      }
    }
    console.log('');

    // 测试5: 等待一段时间后再次检查
    console.log('⏳ 测试5: 等待3秒后再次检查...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    try {
      const { stdout } = await execAsync('redis-cli -h 192.168.200.200 -p 6379 -a 123456 keys "*"');
      const allKeys = stdout.trim().split('\n').filter(key => key.trim());
      console.log(`Redis中总共有 ${allKeys.length} 个键`);
      
      // 查找可能相关的键
      const relevantKeys = allKeys.filter(key => 
        key.includes('leaderboard') || 
        key.includes('test') || 
        key.includes('rankserver') ||
        key.includes('cache')
      );
      
      if (relevantKeys.length > 0) {
        console.log('可能相关的键:');
        relevantKeys.forEach(key => console.log(`  - ${key}`));
      } else {
        console.log('没有找到相关的键');
      }
    } catch (error) {
      console.error('检查所有键失败:', error.message);
    }
    console.log('');

    // 测试6: 再次读取缓存确认数据仍然存在
    console.log('📖 测试6: 再次读取缓存确认数据存在');
    const cachedData2 = await cacheManager.get(testKey);
    if (cachedData2) {
      console.log('✅ 缓存数据仍然存在');
    } else {
      console.log('❌ 缓存数据已丢失');
    }

    // 清理测试数据
    await cacheManager.del(testKey);
    console.log('🗑️ 测试数据已清理');

    console.log('\n🎉 缓存持久化测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

if (require.main === module) {
  testCachePersistence();
}

module.exports = testCachePersistence;
