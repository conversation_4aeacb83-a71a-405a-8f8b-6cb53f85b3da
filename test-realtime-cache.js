#!/usr/bin/env node

/**
 * 实时缓存测试
 * 生成排行榜后立即检查Redis状态
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/app.module');
const { LeaderboardService } = require('./dist/modules/leaderboard/leaderboard.service');
const { CACHE_MANAGER } = require('@nestjs/cache-manager');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

async function testRealtimeCache() {
  console.log('🔍 实时缓存测试...\n');

  let app;
  try {
    // 创建应用实例
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: ['error'],
    });

    const leaderboardService = app.get(LeaderboardService);
    const cacheManager = app.get(CACHE_MANAGER);

    // 步骤1: 清除现有缓存
    console.log('🗑️ 步骤1: 清除现有缓存');
    await leaderboardService.clearCache('coins');
    console.log('✅ coins缓存已清除');

    // 检查Redis状态
    console.log('\n📊 检查清除后的Redis状态:');
    await checkRedisState();

    // 步骤2: 生成排行榜
    console.log('\n📊 步骤2: 生成coins排行榜');
    console.log('开始生成...');
    await leaderboardService.generateLeaderboard('coins');
    console.log('✅ coins排行榜生成完成');

    // 步骤3: 立即检查应用内缓存
    console.log('\n📖 步骤3: 立即检查应用内缓存');
    const cachedData = await cacheManager.get('leaderboard:coins');
    if (cachedData) {
      console.log('✅ 应用内缓存存在');
      const data = typeof cachedData === 'string' ? JSON.parse(cachedData) : cachedData;
      console.log(`  - 排行榜条目: ${data.leaderboard?.length || 0}`);
      console.log(`  - 总玩家数: ${data.totalPlayers || 0}`);
    } else {
      console.log('❌ 应用内缓存不存在');
    }

    // 步骤4: 立即检查Redis状态
    console.log('\n🔍 步骤4: 立即检查Redis状态');
    await checkRedisState();

    // 步骤5: 等待5秒后再次检查
    console.log('\n⏳ 步骤5: 等待5秒后再次检查...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    await checkRedisState();

    // 步骤6: 通过服务读取排行榜
    console.log('\n📖 步骤6: 通过服务读取排行榜');
    const result = await leaderboardService.getLeaderboard('coins', 3);
    console.log(`✅ 读取成功，来源: ${result.fromCache ? '缓存' : '数据库'}`);
    console.log(`  - 排行榜条目: ${result.leaderboard.length}`);

    // 步骤7: 再次检查Redis状态
    console.log('\n🔍 步骤7: 读取后的Redis状态');
    await checkRedisState();

    console.log('\n🎉 实时缓存测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

async function checkRedisState() {
  try {
    // 检查namespace:rankserver成员
    const { stdout: members } = await execAsync('redis-cli -h 192.168.200.200 -p 6379 -a 123456 smembers "namespace:rankserver"');
    const memberList = members.trim().split('\n').map(line => line.trim()).filter(line => line);
    const leaderboardMembers = memberList.filter(member => member.includes('leaderboard'));
    
    console.log(`  - namespace:rankserver 总成员: ${memberList.length}`);
    console.log(`  - 排行榜相关成员: ${leaderboardMembers.length}`);
    
    if (leaderboardMembers.length > 0) {
      console.log('  - 排行榜成员:');
      leaderboardMembers.forEach(member => {
        console.log(`    * ${member}`);
      });
      
      // 检查第一个排行榜成员的值
      const firstMember = leaderboardMembers[0];
      try {
        const { stdout: value } = await execAsync(`redis-cli -h 192.168.200.200 -p 6379 -a 123456 get "${firstMember}"`);
        if (value.trim() && value.trim() !== '(nil)') {
          console.log(`  - ${firstMember} 值存在 (${value.trim().length} 字符)`);
        } else {
          console.log(`  - ${firstMember} 值不存在`);
        }
      } catch (error) {
        console.log(`  - ${firstMember} 检查失败: ${error.message}`);
      }
    }
    
    // 检查所有rankserver键
    const { stdout: allKeys } = await execAsync('redis-cli -h 192.168.200.200 -p 6379 -a 123456 keys "*rankserver*"');
    const keyList = allKeys.trim().split('\n').map(line => line.trim()).filter(line => line);
    console.log(`  - 所有rankserver键: ${keyList.length}`);
    
  } catch (error) {
    console.log(`  - Redis检查失败: ${error.message}`);
  }
}

if (require.main === module) {
  testRealtimeCache();
}

module.exports = testRealtimeCache;
