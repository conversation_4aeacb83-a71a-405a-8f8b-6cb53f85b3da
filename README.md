# 排行榜服务器 (RankServer)

基于 NestJS + MySQL + Redis 构建的简洁易维护的排行榜服务器系统。

## 🚀 功能特性

- **玩家数据上报**: 客户端可上报玩家基础数据和金币信息
- **排行榜查询**: 高性能的排行榜数据查询，支持缓存
- **定时更新**: 支持定时（每日凌晨0点）或手动触发排行榜更新
- **多级缓存**: Redis + 数据库双重缓存策略
- **API文档**: 集成 Swagger 自动生成 API 文档
- **Docker支持**: 一键启动开发环境

## 🛠 技术栈

- **框架**: NestJS (TypeScript)
- **数据库**: MySQL 8.0
- **缓存**: Redis 7
- **ORM**: TypeORM
- **API文档**: Swagger/OpenAPI
- **容器化**: Docker & Docker Compose

## 📋 系统要求

- Node.js >= 18
- MySQL >= 8.0
- Redis >= 6.0
- Docker & Docker Compose (可选)

## 🚀 快速开始

### 方式一：Docker 启动（推荐）

1. **克隆项目**
```bash
git clone <repository-url>
cd RankServer
```

2. **启动服务**
```bash
docker-compose up -d
```

3. **访问服务**
- API服务: http://localhost:3000
- API文档: http://localhost:3000/api/docs

### 方式二：本地开发

1. **安装依赖**
```bash
npm install
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和Redis连接信息
```

3. **启动数据库和Redis**
```bash
# 使用 Docker 启动数据库服务
docker-compose up -d mysql redis
```

4. **启动应用**
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

## 📚 API 接口

### 玩家数据上报

```http
POST /api/players/report
Content-Type: application/json

{
  "id": 12345,
  "username": "player001",
  "avatar": "https://example.com/avatar.jpg",
  "coins": 10000
}
```

### 获取排行榜

```http
GET /api/leaderboard?limit=50
```

### 手动刷新排行榜

```http
POST /api/leaderboard/refresh
```

## 🏗 项目结构

```
src/
├── config/                 # 配置文件
│   ├── database.config.ts  # 数据库配置
│   └── redis.config.ts     # Redis配置
├── entities/               # 数据库实体
│   ├── player.entity.ts    # 玩家实体
│   └── leaderboard-cache.entity.ts # 排行榜缓存实体
├── modules/                # 业务模块
│   ├── player/            # 玩家模块
│   ├── leaderboard/       # 排行榜模块
│   └── scheduler/         # 定时任务模块
├── app.module.ts          # 根模块
└── main.ts               # 应用入口
```

## ⚙️ 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DB_HOST` | 数据库主机 | localhost |
| `DB_PORT` | 数据库端口 | 3306 |
| `DB_USERNAME` | 数据库用户名 | root |
| `DB_PASSWORD` | 数据库密码 | password |
| `DB_DATABASE` | 数据库名 | rank_server |
| `REDIS_HOST` | Redis主机 | localhost |
| `REDIS_PORT` | Redis端口 | 6379 |
| `PORT` | 应用端口 | 3000 |
| `LEADERBOARD_SIZE` | 排行榜条数 | 100 |
| `LEADERBOARD_CACHE_TTL` | 缓存TTL(秒) | 86400 |
| `LEADERBOARD_UPDATE_CRON` | 定时更新表达式 | 0 0 0 * * * |

### 定时任务

- **排行榜更新**: 每日凌晨0点自动更新排行榜
- **数据清理**: 每小时执行一次数据清理任务

## 🧪 测试

```bash
# 单元测试
npm run test

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

## 📈 性能优化

1. **数据库索引**: 在金币字段和更新时间字段上创建索引
2. **Redis缓存**: 排行榜数据优先从Redis获取
3. **批量操作**: 排行榜生成使用批量插入
4. **异步处理**: 手动刷新排行榜异步执行，不阻塞响应

## 🔧 运维指南

### 监控指标

- API响应时间
- 数据库连接池状态
- Redis缓存命中率
- 排行榜更新频率和耗时

### 日志

应用使用 NestJS 内置的 Logger，日志级别可通过环境变量控制。

### 备份

建议定期备份 MySQL 数据库：

```bash
mysqldump -u root -p rank_server > backup_$(date +%Y%m%d_%H%M%S).sql
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请创建 [Issue](../../issues) 或联系开发团队。
