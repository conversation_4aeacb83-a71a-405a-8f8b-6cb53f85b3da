#!/usr/bin/env node

/**
 * 调试Keyv存储机制
 * 分析Keyv如何在Redis中存储数据
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/app.module');
const { CACHE_MANAGER } = require('@nestjs/cache-manager');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

async function debugKeyvStorage() {
  console.log('🔍 调试Keyv存储机制...\n');

  let app;
  try {
    // 创建应用实例
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: ['error'],
    });

    const cacheManager = app.get(CACHE_MANAGER);

    // 测试1: 写入一个测试键并观察Redis变化
    console.log('📝 测试1: 写入测试键');
    const testKey = 'leaderboard:debug-test';
    const testData = {
      message: 'Debug test data',
      timestamp: Date.now(),
      leaderboard: [
        { rank: 1, id: 'test1', username: 'TestUser1', value: 100 }
      ]
    };

    await cacheManager.set(testKey, testData, 60000);
    console.log('✅ 测试键写入完成');
    console.log('键:', testKey);
    console.log('数据:', JSON.stringify(testData, null, 2));
    console.log('');

    // 测试2: 检查Redis中的所有rankserver相关键
    console.log('🔍 测试2: 检查Redis中的rankserver键');
    try {
      const { stdout } = await execAsync('redis-cli -h 192.168.200.200 -p 6379 -a 123456 keys "*rankserver*"');
      console.log('Redis中的rankserver键:');
      if (stdout.trim()) {
        const keys = stdout.trim().split('\n');
        keys.forEach((key, index) => {
          console.log(`  ${index + 1}) ${key}`);
        });
      } else {
        console.log('(没有找到rankserver键)');
      }
    } catch (error) {
      console.error('Redis命令执行失败:', error.message);
    }
    console.log('');

    // 测试3: 检查namespace:rankserver的内容
    console.log('🔍 测试3: 检查namespace:rankserver的内容');
    try {
      const { stdout } = await execAsync('redis-cli -h 192.168.200.200 -p 6379 -a 123456 smembers "namespace:rankserver"');
      console.log('namespace:rankserver 成员:');
      if (stdout.trim()) {
        const members = stdout.trim().split('\n').map(line => line.trim()).filter(line => line);
        // 只显示前10个和排行榜相关的
        const leaderboardMembers = members.filter(member => member.includes('leaderboard'));
        const otherMembers = members.filter(member => !member.includes('leaderboard')).slice(0, 5);
        
        console.log('排行榜相关键:');
        leaderboardMembers.forEach(member => {
          console.log(`  - ${member}`);
        });
        
        if (otherMembers.length > 0) {
          console.log('其他键 (前5个):');
          otherMembers.forEach(member => {
            console.log(`  - ${member}`);
          });
        }
        
        console.log(`总计: ${members.length} 个键`);
      } else {
        console.log('(namespace:rankserver 为空)');
      }
    } catch (error) {
      console.error('Redis命令执行失败:', error.message);
    }
    console.log('');

    // 测试4: 检查具体的排行榜键值
    console.log('🔍 测试4: 检查具体的排行榜键值');
    const leaderboardKeys = [
      'rankserver:leaderboard:coins',
      'rankserver:leaderboard:debug-test'
    ];

    for (const key of leaderboardKeys) {
      try {
        const { stdout } = await execAsync(`redis-cli -h 192.168.200.200 -p 6379 -a 123456 get "${key}"`);
        if (stdout.trim() && stdout.trim() !== '(nil)') {
          console.log(`✅ ${key}: 存在`);
          // 尝试解析数据
          try {
            const data = JSON.parse(stdout.trim().replace(/^"|"$/g, ''));
            console.log(`  - 数据类型: ${typeof data}`);
            if (data && typeof data === 'object') {
              console.log(`  - 键: ${Object.keys(data).join(', ')}`);
              if (data.leaderboard && Array.isArray(data.leaderboard)) {
                console.log(`  - 排行榜条目: ${data.leaderboard.length}`);
              }
            }
          } catch (parseError) {
            console.log(`  - 数据: ${stdout.trim().substring(0, 100)}...`);
          }
        } else {
          console.log(`❌ ${key}: 不存在`);
        }
      } catch (error) {
        console.log(`❌ ${key}: 检查失败 - ${error.message}`);
      }
    }
    console.log('');

    // 测试5: 通过应用读取缓存
    console.log('📖 测试5: 通过应用读取缓存');
    const cachedTestData = await cacheManager.get(testKey);
    if (cachedTestData) {
      console.log('✅ 应用内缓存读取成功');
      console.log('数据:', JSON.stringify(cachedTestData, null, 2));
    } else {
      console.log('❌ 应用内缓存读取失败');
    }
    console.log('');

    // 测试6: 读取排行榜缓存
    console.log('📖 测试6: 读取排行榜缓存');
    const coinsCache = await cacheManager.get('leaderboard:coins');
    if (coinsCache) {
      console.log('✅ coins排行榜缓存存在');
      console.log('数据类型:', typeof coinsCache);
      if (typeof coinsCache === 'string') {
        try {
          const data = JSON.parse(coinsCache);
          console.log('解析后的数据结构:');
          console.log(`  - leaderboard: ${Array.isArray(data.leaderboard) ? data.leaderboard.length + '条记录' : '非数组'}`);
          console.log(`  - totalPlayers: ${data.totalPlayers}`);
          console.log(`  - generatedAt: ${data.generatedAt}`);
        } catch (error) {
          console.log('JSON解析失败:', error.message);
        }
      } else {
        console.log('缓存数据:', JSON.stringify(coinsCache, null, 2));
      }
    } else {
      console.log('❌ coins排行榜缓存不存在');
    }

    // 清理测试数据
    await cacheManager.del(testKey);
    console.log('\n🗑️ 测试数据已清理');

    console.log('\n🎉 Keyv存储机制调试完成！');

  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

if (require.main === module) {
  debugKeyvStorage();
}

module.exports = debugKeyvStorage;
