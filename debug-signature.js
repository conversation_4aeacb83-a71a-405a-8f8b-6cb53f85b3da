const crypto = require('crypto');

// 测试数据
const testData = {
  id: 4001,
  username: "Speed1K_1",
  avatar: "https://example.com/speed1.jpg",
  timeMs: 5500,
  targetCoins: 1000
};

const secretKey = 'your-super-secret-key-change-in-production-environment';
const timestamp = Date.now();
const nonce = crypto.randomBytes(16).toString('hex');

console.log('🔍 签名调试信息:');
console.log('密钥:', secretKey);
console.log('时间戳:', timestamp);
console.log('随机数:', nonce);
console.log('原始数据:', JSON.stringify(testData));

// 客户端签名生成逻辑
function sortObjectKeys(obj) {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => sortObjectKeys(item));
  }

  const sortedKeys = Object.keys(obj).sort();
  const sortedObj = {};
  
  for (const key of sortedKeys) {
    sortedObj[key] = sortObjectKeys(obj[key]);
  }
  
  return sortedObj;
}

const sortedData = sortObjectKeys(testData);
console.log('排序后数据:', JSON.stringify(sortedData));

const signString = `${timestamp}${nonce}${JSON.stringify(sortedData)}${secretKey}`;
console.log('签名字符串:', signString);

const signature = crypto.createHash('sha256').update(signString).digest('hex');
console.log('生成的签名:', signature);

// 验证签名（模拟服务端逻辑）
const expectedSignature = crypto.createHash('sha256').update(signString).digest('hex');
console.log('期望的签名:', expectedSignature);
console.log('签名匹配:', signature === expectedSignature);

// 测试HTTP请求
const http = require('http');

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/api/stats/time-challenge/report',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'x-signature': signature,
    'x-timestamp': timestamp.toString(),
    'x-nonce': nonce,
  }
};

console.log('\n📡 发送测试请求...');
console.log('请求头:', options.headers);

const req = http.request(options, (res) => {
  let body = '';
  res.on('data', (chunk) => {
    body += chunk;
  });
  res.on('end', () => {
    console.log('\n📥 服务器响应:');
    console.log('状态码:', res.statusCode);
    console.log('响应体:', body);
  });
});

req.on('error', (e) => {
  console.error('请求错误:', e.message);
});

req.write(JSON.stringify(testData));
req.end();
