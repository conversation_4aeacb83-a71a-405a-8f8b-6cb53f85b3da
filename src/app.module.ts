import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { getDatabaseConfig } from './config/database.config';
import { StatsModule } from './modules/stats/stats.module';
import { LeaderboardModule } from './modules/leaderboard/leaderboard.module';
import { SchedulerModule } from './modules/scheduler/scheduler.module';
import { SecurityModule } from './common/security/security.module';

/**
 * 应用根模块
 * 配置全局模块和服务
 */
@Module({
  imports: [
    // 配置模块 - 加载环境变量
    ConfigModule.forRoot({
      isGlobal: true, // 全局可用
      envFilePath: ['.env.local', '.env'], // 环境变量文件路径
      cache: true, // 缓存环境变量以提高性能
    }),

    // 数据库模块 - TypeORM + MySQL
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: getDatabaseConfig,
      inject: [ConfigService],
    }),

    // 新的Redis缓存模块 - 基于ioredis
    // 注意：新的CacheModule已经在各个模块中单独导入

    // 安全模块
    SecurityModule,

    // 业务模块
    StatsModule,
    LeaderboardModule,
    SchedulerModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
