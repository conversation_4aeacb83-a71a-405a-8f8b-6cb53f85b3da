import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheModule } from '@nestjs/cache-manager';
import { getDatabaseConfig } from './config/database.config';
import { getRedisConfig } from './config/redis.config';
import { StatsModule } from './modules/stats/stats.module';
import { LeaderboardModule } from './modules/leaderboard/leaderboard.module';
import { SchedulerModule } from './modules/scheduler/scheduler.module';
import { SecurityModule } from './common/security/security.module';

/**
 * 应用根模块
 * 配置全局模块和服务
 */
@Module({
  imports: [
    // 配置模块 - 加载环境变量
    ConfigModule.forRoot({
      isGlobal: true, // 全局可用
      envFilePath: ['.env.local', '.env'], // 环境变量文件路径
      cache: true, // 缓存环境变量以提高性能
    }),

    // 数据库模块 - TypeORM + MySQL
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: getDatabaseConfig,
      inject: [ConfigService],
    }),

    // 缓存模块 - Redis
    CacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: getRedisConfig,
      inject: [ConfigService],
      isGlobal: true, // 全局可用
    }),

    // 安全模块
    SecurityModule,

    // 业务模块
    StatsModule,
    LeaderboardModule,
    SchedulerModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
