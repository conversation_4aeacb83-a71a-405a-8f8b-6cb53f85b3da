import { Injectable, Logger, OnModuleD<PERSON>roy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

/**
 * 基于ioredis的Redis缓存服务
 * 提供直接、透明的Redis缓存操作
 */
@Injectable()
export class RedisCacheService implements OnModuleDestroy {
  private readonly logger = new Logger(RedisCacheService.name);
  private readonly redis: Redis;
  private readonly keyPrefix: string;

  constructor(private readonly configService: ConfigService) {
    // 创建Redis连接
    this.redis = new Redis({
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD') || undefined,
      db: this.configService.get<number>('REDIS_DB', 0),
      retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        return delay;
      },
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keepAlive: 30000,
      connectTimeout: 10000,
      commandTimeout: 5000,
      family: 4, // IPv4
    });

    // 设置键前缀
    this.keyPrefix = 'rankserver:';

    // 监听连接事件
    this.redis.on('connect', () => {
      this.logger.log('Redis连接已建立');
    });

    this.redis.on('ready', () => {
      this.logger.log('Redis连接就绪');
    });

    this.redis.on('error', (error) => {
      this.logger.error(`Redis连接错误: ${error.message}`, error.stack);
    });

    this.redis.on('close', () => {
      this.logger.warn('Redis连接已关闭');
    });

    this.redis.on('reconnecting', () => {
      this.logger.log('Redis正在重连...');
    });
  }

  /**
   * 生成完整的缓存键
   */
  private getFullKey(key: string): string {
    return `${this.keyPrefix}${key}`;
  }

  /**
   * 设置缓存
   * @param key 缓存键
   * @param value 缓存值
   * @param ttlSeconds TTL（秒），默认24小时
   */
  async set(key: string, value: any, ttlSeconds?: number): Promise<void> {
    try {
      const fullKey = this.getFullKey(key);
      const serializedValue = JSON.stringify(value);
      const defaultTtl = this.configService.get<number>('LEADERBOARD_CACHE_TTL', 86400);
      const ttl = ttlSeconds || defaultTtl;

      if (ttl > 0) {
        await this.redis.setex(fullKey, ttl, serializedValue);
      } else {
        await this.redis.set(fullKey, serializedValue);
      }

      this.logger.debug(`缓存已设置: ${fullKey} (TTL: ${ttl}s)`);
    } catch (error) {
      this.logger.error(`设置缓存失败 [${key}]: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取缓存
   * @param key 缓存键
   * @returns 缓存值，不存在时返回null
   */
  async get<T = any>(key: string): Promise<T | null> {
    try {
      const fullKey = this.getFullKey(key);
      const value = await this.redis.get(fullKey);

      if (value === null) {
        this.logger.debug(`缓存未命中: ${fullKey}`);
        return null;
      }

      const parsedValue = JSON.parse(value);
      this.logger.debug(`缓存命中: ${fullKey}`);
      return parsedValue;
    } catch (error) {
      this.logger.error(`获取缓存失败 [${key}]: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 删除缓存
   * @param key 缓存键
   */
  async del(key: string): Promise<void> {
    try {
      const fullKey = this.getFullKey(key);
      const result = await this.redis.del(fullKey);
      
      if (result > 0) {
        this.logger.debug(`缓存已删除: ${fullKey}`);
      } else {
        this.logger.debug(`缓存不存在: ${fullKey}`);
      }
    } catch (error) {
      this.logger.error(`删除缓存失败 [${key}]: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 检查缓存是否存在
   * @param key 缓存键
   */
  async exists(key: string): Promise<boolean> {
    try {
      const fullKey = this.getFullKey(key);
      const result = await this.redis.exists(fullKey);
      return result === 1;
    } catch (error) {
      this.logger.error(`检查缓存存在性失败 [${key}]: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 获取缓存TTL
   * @param key 缓存键
   * @returns TTL（秒），-1表示永不过期，-2表示不存在
   */
  async ttl(key: string): Promise<number> {
    try {
      const fullKey = this.getFullKey(key);
      return await this.redis.ttl(fullKey);
    } catch (error) {
      this.logger.error(`获取缓存TTL失败 [${key}]: ${error.message}`, error.stack);
      return -2;
    }
  }

  /**
   * 获取所有匹配的键
   * @param pattern 键模式
   */
  async keys(pattern: string): Promise<string[]> {
    try {
      const fullPattern = this.getFullKey(pattern);
      const keys = await this.redis.keys(fullPattern);
      // 移除前缀，返回原始键名
      return keys.map(key => key.replace(this.keyPrefix, ''));
    } catch (error) {
      this.logger.error(`获取键列表失败 [${pattern}]: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * 清除所有匹配的缓存
   * @param pattern 键模式
   */
  async clear(pattern: string = '*'): Promise<number> {
    try {
      const fullPattern = this.getFullKey(pattern);
      const keys = await this.redis.keys(fullPattern);
      
      if (keys.length === 0) {
        this.logger.debug(`没有找到匹配的键: ${fullPattern}`);
        return 0;
      }

      const result = await this.redis.del(...keys);
      this.logger.log(`已清除 ${result} 个缓存键 (模式: ${fullPattern})`);
      return result;
    } catch (error) {
      this.logger.error(`清除缓存失败 [${pattern}]: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * 获取Redis连接信息
   */
  async getInfo(): Promise<{
    status: string;
    host: string;
    port: number;
    db: number;
    keyCount: number;
  }> {
    try {
      const info = await this.redis.info('server');
      const keyCount = await this.redis.dbsize();
      
      return {
        status: this.redis.status,
        host: this.redis.options.host || 'localhost',
        port: this.redis.options.port || 6379,
        db: this.redis.options.db || 0,
        keyCount,
      };
    } catch (error) {
      this.logger.error(`获取Redis信息失败: ${error.message}`, error.stack);
      return {
        status: 'error',
        host: 'unknown',
        port: 0,
        db: 0,
        keyCount: 0,
      };
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.redis.ping();
      return result === 'PONG';
    } catch (error) {
      this.logger.error(`Redis健康检查失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 模块销毁时关闭连接
   */
  async onModuleDestroy(): Promise<void> {
    try {
      await this.redis.quit();
      this.logger.log('Redis连接已关闭');
    } catch (error) {
      this.logger.error(`关闭Redis连接失败: ${error.message}`, error.stack);
    }
  }
}
