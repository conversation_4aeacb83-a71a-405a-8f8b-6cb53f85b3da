/**
 * 排行榜类型枚举
 */
export enum LeaderboardType {
  COINS = 'coins',
  TIME_CHALLENGE = 'time_challenge',
}

/**
 * 排行榜配置接口
 */
export interface LeaderboardConfig {
  type: LeaderboardType;
  name: string;
  description: string;
  sortOrder: 'ASC' | 'DESC';
  unit: string;
  formatValue: (value: number) => string;
}

/**
 * 排行榜配置映射
 */
export const LEADERBOARD_CONFIGS: Record<LeaderboardType, LeaderboardConfig> = {
  [LeaderboardType.COINS]: {
    type: LeaderboardType.COINS,
    name: '金币排行榜',
    description: '按玩家金币数量排序',
    sortOrder: 'DESC',
    unit: '金币',
    formatValue: (value: number) => `${value.toLocaleString()} 金币`,
  },
  [LeaderboardType.TIME_CHALLENGE]: {
    type: LeaderboardType.TIME_CHALLENGE,
    name: '速度挑战排行榜',
    description: '按完成固定金币挑战的耗时排序',
    sortOrder: 'ASC',
    unit: '毫秒',
    formatValue: (value: number) => {
      const seconds = Math.floor(value / 1000);
      const milliseconds = value % 1000;
      return `${seconds}.${milliseconds.toString().padStart(3, '0')}s`;
    },
  },
};
