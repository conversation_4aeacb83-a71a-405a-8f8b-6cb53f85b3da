/**
 * 排行榜类型枚举
 */
export enum LeaderboardType {
  COINS = 'coins',
  TIME_CHALLENGE = 'time_challenge',
}

/**
 * 挑战目标配置
 */
export interface ChallengeTarget {
  coins: number;
  name: string;
  description: string;
  displayName: string;
}

/**
 * 预定义的挑战目标
 */
export const CHALLENGE_TARGETS: ChallengeTarget[] = [
  {
    coins: 1000,
    name: 'challenge_1k',
    description: '最快达到1000金币挑战',
    displayName: '1K金币挑战',
  },
  {
    coins: 10000,
    name: 'challenge_10k',
    description: '最快达到1万金币挑战',
    displayName: '1万金币挑战',
  },
  {
    coins: 100000,
    name: 'challenge_100k',
    description: '最快达到10万金币挑战',
    displayName: '10万金币挑战',
  },
  {
    coins: 1000000,
    name: 'challenge_1m',
    description: '最快达到100万金币挑战',
    displayName: '100万金币挑战',
  },
  {
    coins: 100000000,
    name: 'challenge_100m',
    description: '最快达到1亿金币挑战',
    displayName: '1亿金币挑战',
  },
];

/**
 * 根据金币数量获取挑战目标配置
 */
export function getChallengeTargetByCoins(coins: number): ChallengeTarget | undefined {
  return CHALLENGE_TARGETS.find(target => target.coins === coins);
}

/**
 * 获取所有可用的挑战目标
 */
export function getAllChallengeTargets(): ChallengeTarget[] {
  return [...CHALLENGE_TARGETS];
}

/**
 * 生成挑战排行榜类型标识
 */
export function generateChallengeLeaderboardType(targetCoins: number): string {
  return `time_challenge_${targetCoins}`;
}

/**
 * 排行榜配置接口
 */
export interface LeaderboardConfig {
  type: LeaderboardType;
  name: string;
  description: string;
  sortOrder: 'ASC' | 'DESC';
  unit: string;
  formatValue: (value: number) => string;
}

/**
 * 排行榜配置映射
 */
export const LEADERBOARD_CONFIGS: Record<LeaderboardType, LeaderboardConfig> = {
  [LeaderboardType.COINS]: {
    type: LeaderboardType.COINS,
    name: '金币排行榜',
    description: '按玩家金币数量排序',
    sortOrder: 'DESC',
    unit: '金币',
    formatValue: (value: number) => `${value.toLocaleString()} 金币`,
  },
  [LeaderboardType.TIME_CHALLENGE]: {
    type: LeaderboardType.TIME_CHALLENGE,
    name: '速度挑战排行榜',
    description: '按完成固定金币挑战的耗时排序',
    sortOrder: 'ASC',
    unit: '毫秒',
    formatValue: (value: number) => {
      const seconds = Math.floor(value / 1000);
      const milliseconds = value % 1000;
      return `${seconds}.${milliseconds.toString().padStart(3, '0')}s`;
    },
  },
};
