/**
 * 排行榜类型枚举
 */
export enum LeaderboardType {
  COINS = 'coins',
  TIME_CHALLENGE = 'time_challenge',
}

/**
 * 挑战目标配置
 */
export interface ChallengeTarget {
  coins: number;
  name: string;
  description: string;
  displayName: string;
}

/**
 * 预定义的挑战目标
 */
export const CHALLENGE_TARGETS: ChallengeTarget[] = [
  {
    coins: 1000,
    name: 'challenge_1k',
    description: '最快达到1000金币挑战',
    displayName: '1K金币挑战',
  },
  {
    coins: 10000,
    name: 'challenge_10k',
    description: '最快达到1万金币挑战',
    displayName: '1万金币挑战',
  },
  {
    coins: 100000,
    name: 'challenge_100k',
    description: '最快达到10万金币挑战',
    displayName: '10万金币挑战',
  },
  {
    coins: 1000000,
    name: 'challenge_1m',
    description: '最快达到100万金币挑战',
    displayName: '100万金币挑战',
  },
  {
    coins: 100000000,
    name: 'challenge_100m',
    description: '最快达到1亿金币挑战',
    displayName: '1亿金币挑战',
  },
];

/**
 * 根据金币数量获取挑战目标配置
 */
export function getChallengeTargetByCoins(coins: number): ChallengeTarget | undefined {
  return CHALLENGE_TARGETS.find(target => target.coins === coins);
}

/**
 * 获取所有可用的挑战目标
 */
export function getAllChallengeTargets(): ChallengeTarget[] {
  return [...CHALLENGE_TARGETS];
}

/**
 * 生成挑战排行榜类型标识
 */
export function generateChallengeLeaderboardType(targetCoins: number): string {
  return `time_challenge_${targetCoins}`;
}

/**
 * 排行榜配置接口
 */
export interface LeaderboardConfig {
  type: LeaderboardType;
  name: string;
  description: string;
  sortOrder: 'ASC' | 'DESC';
  unit: string;
  formatValue: (value: number) => string;
}

/**
 * 排行榜配置映射
 */
export const LEADERBOARD_CONFIGS: Record<string, LeaderboardConfig> = {
  [LeaderboardType.COINS]: {
    type: LeaderboardType.COINS,
    name: '金币排行榜',
    description: '按玩家金币数量排序',
    sortOrder: 'DESC',
    unit: '金币',
    formatValue: (value: number) => `${value.toLocaleString()} 金币`,
  },
};

/**
 * 获取排行榜配置（包括动态生成的挑战排行榜）
 */
export function getLeaderboardConfig(leaderboardType: string): LeaderboardConfig {
  // 如果是预定义的配置，直接返回
  if (LEADERBOARD_CONFIGS[leaderboardType]) {
    return LEADERBOARD_CONFIGS[leaderboardType];
  }

  // 如果是挑战排行榜，动态生成配置
  if (leaderboardType.startsWith('time_challenge_')) {
    const targetCoins = parseInt(leaderboardType.replace('time_challenge_', ''));
    const challengeTarget = getChallengeTargetByCoins(targetCoins);

    if (challengeTarget) {
      return {
        type: leaderboardType as LeaderboardType,
        name: challengeTarget.displayName,
        description: challengeTarget.description,
        sortOrder: 'ASC',
        unit: '毫秒',
        formatValue: (value: number) => {
          const seconds = Math.floor(value / 1000);
          const milliseconds = value % 1000;
          return `${seconds}.${milliseconds.toString().padStart(3, '0')}s`;
        },
      };
    }
  }

  // 默认配置
  return {
    type: leaderboardType as LeaderboardType,
    name: '未知排行榜',
    description: '未知排行榜类型',
    sortOrder: 'DESC',
    unit: '',
    formatValue: (value: number) => value.toString(),
  };
}
