import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisCacheService } from '../cache/redis-cache.service';

/**
 * 频率限制配置
 */
export interface RateLimitConfig {
  windowMs: number; // 时间窗口（毫秒）
  maxRequests: number; // 最大请求次数
}

/**
 * 频率限制服务
 * 防止客户端频繁请求和滥用
 */
@Injectable()
export class RateLimitService {
  private readonly configs: Record<string, RateLimitConfig>;

  constructor(
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
    private readonly configService: ConfigService,
  ) {
    // 配置不同接口的频率限制
    this.configs = {
      // 金币上报：每分钟最多10次
      'coins_report': {
        windowMs: 60 * 1000,
        maxRequests: 10,
      },
      // 耗时挑战上报：每分钟最多5次
      'time_challenge_report': {
        windowMs: 60 * 1000,
        maxRequests: 5,
      },
      // 排行榜查询：每分钟最多30次
      'leaderboard_query': {
        windowMs: 60 * 1000,
        maxRequests: 30,
      },
      // 排行榜刷新：每小时最多3次
      'leaderboard_refresh': {
        windowMs: 60 * 60 * 1000,
        maxRequests: 3,
      },
    };
  }

  /**
   * 检查频率限制
   * @param key 限制键（通常是 接口类型:玩家ID 或 接口类型:IP）
   * @param type 限制类型
   * @returns 是否允许请求
   */
  async checkRateLimit(key: string, type: string): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
  }> {
    const config = this.configs[type];
    if (!config) {
      // 如果没有配置，默认允许
      return { allowed: true, remaining: 999, resetTime: 0 };
    }

    const cacheKey = `rate_limit:${type}:${key}`;
    const now = Date.now();
    const windowStart = now - config.windowMs;

    // 获取当前时间窗口内的请求记录
    const requests = await this.getRequestsInWindow(cacheKey, windowStart, now);
    
    const currentCount = requests.length;
    const remaining = Math.max(0, config.maxRequests - currentCount - 1);
    const resetTime = windowStart + config.windowMs;

    if (currentCount >= config.maxRequests) {
      return { allowed: false, remaining: 0, resetTime };
    }

    // 记录本次请求
    await this.recordRequest(cacheKey, now, config.windowMs);

    return { allowed: true, remaining, resetTime };
  }

  /**
   * 获取时间窗口内的请求记录
   * @param cacheKey 缓存键
   * @param windowStart 窗口开始时间
   * @param now 当前时间
   * @returns 请求时间戳数组
   */
  private async getRequestsInWindow(cacheKey: string, windowStart: number, now: number): Promise<number[]> {
    const cached = await this.cacheManager.get<number[]>(cacheKey);
    if (!cached) {
      return [];
    }

    // 过滤出时间窗口内的请求
    return cached.filter(timestamp => timestamp > windowStart);
  }

  /**
   * 记录请求
   * @param cacheKey 缓存键
   * @param timestamp 请求时间戳
   * @param windowMs 时间窗口
   */
  private async recordRequest(cacheKey: string, timestamp: number, windowMs: number): Promise<void> {
    const cached = await this.cacheManager.get<number[]>(cacheKey) || [];
    const windowStart = timestamp - windowMs;
    
    // 清理过期记录并添加新记录
    const updatedRequests = cached
      .filter(ts => ts > windowStart)
      .concat(timestamp);

    // 设置缓存，TTL为时间窗口的2倍以确保数据不会过早清理
    await this.cacheManager.set(cacheKey, updatedRequests, windowMs * 2);
  }

  /**
   * 重置频率限制（管理员功能）
   * @param key 限制键
   * @param type 限制类型
   */
  async resetRateLimit(key: string, type: string): Promise<void> {
    const cacheKey = `rate_limit:${type}:${key}`;
    await this.cacheManager.del(cacheKey);
  }

  /**
   * 获取频率限制配置
   * @param type 限制类型
   * @returns 配置信息
   */
  getRateLimitConfig(type: string): RateLimitConfig | null {
    return this.configs[type] || null;
  }
}
