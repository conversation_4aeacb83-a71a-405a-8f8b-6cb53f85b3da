import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisCacheService } from '../cache/redis-cache.service';

/**
 * 防重放攻击服务
 * 通过记录已使用的nonce来防止重放攻击
 */
@Injectable()
export class AntiReplayService {
  private readonly nonceTtl: number;

  constructor(
    private readonly redisCacheService: RedisCacheService,
    private readonly configService: ConfigService,
  ) {
    // nonce有效期，默认10分钟（秒）
    this.nonceTtl = this.configService.get<number>('NONCE_TTL', 600);
  }

  /**
   * 检查并记录nonce
   * @param nonce 随机数
   * @returns 如果nonce未被使用过返回true，否则返回false
   */
  async checkAndRecordNonce(nonce: string): Promise<boolean> {
    const cacheKey = `nonce:${nonce}`;

    // 检查nonce是否已存在
    const exists = await this.redisCacheService.exists(cacheKey);
    if (exists) {
      return false; // nonce已被使用
    }

    // 记录nonce
    await this.redisCacheService.set(cacheKey, true, this.nonceTtl);
    return true; // nonce可用
  }

  /**
   * 清理过期的nonce记录（由缓存TTL自动处理，此方法用于手动清理）
   */
  async cleanupExpiredNonces(): Promise<void> {
    // Redis缓存会自动清理过期的键，这里可以添加额外的清理逻辑
    // 如果使用内存缓存，可能需要手动清理
  }
}
