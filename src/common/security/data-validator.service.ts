import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * 数据验证规则
 */
export interface ValidationRule {
  min?: number;
  max?: number;
  allowedValues?: any[];
  pattern?: RegExp;
  customValidator?: (value: any) => boolean;
}

/**
 * 验证结果
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  suspiciousLevel: 'low' | 'medium' | 'high';
}

/**
 * 数据验证服务
 * 验证上报数据的合理性，检测异常数据
 */
@Injectable()
export class DataValidatorService {
  private readonly logger = new Logger(DataValidatorService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * 验证金币数据
   * @param coins 金币数量
   * @param playerId 玩家ID
   * @param previousCoins 玩家之前的金币数量
   * @returns 验证结果
   */
  validateCoinsData(coins: number, playerId: number, previousCoins?: number): ValidationResult {
    const errors: string[] = [];
    let suspiciousLevel: 'low' | 'medium' | 'high' = 'low';

    // 基础范围检查
    if (coins < 0) {
      errors.push('金币数量不能为负数');
      suspiciousLevel = 'high';
    }

    if (coins > 999999999999) {
      errors.push('金币数量超出最大限制');
      suspiciousLevel = 'high';
    }

    // 增长合理性检查
    if (previousCoins !== undefined) {
      const increase = coins - previousCoins;
      
      // 检查是否有异常大的增长
      if (increase > 1000000) { // 单次增长超过100万
        errors.push('金币增长异常，单次增长过大');
        suspiciousLevel = 'high';
      }
      
      // 检查是否有异常的减少（正常情况下金币应该只增不减）
      if (increase < -10000) { // 减少超过1万
        errors.push('金币异常减少');
        suspiciousLevel = 'medium';
      }
    }

    // 数值合理性检查
    if (coins > 0 && coins < 10) {
      // 金币数量过小可能是测试数据
      suspiciousLevel = 'medium';
    }

    return {
      isValid: errors.length === 0,
      errors,
      suspiciousLevel,
    };
  }

  /**
   * 验证耗时挑战数据
   * @param timeMs 耗时（毫秒）
   * @param targetCoins 目标金币
   * @param playerId 玩家ID
   * @param previousBestTime 玩家之前的最佳时间
   * @returns 验证结果
   */
  validateTimeChallengeData(
    timeMs: number, 
    targetCoins: number, 
    playerId: number, 
    previousBestTime?: number
  ): ValidationResult {
    const errors: string[] = [];
    let suspiciousLevel: 'low' | 'medium' | 'high' = 'low';

    // 基础范围检查
    if (timeMs <= 0) {
      errors.push('耗时必须大于0');
      suspiciousLevel = 'high';
    }

    if (timeMs > 3600000) { // 超过1小时
      errors.push('耗时不能超过1小时');
      suspiciousLevel = 'high';
    }

    // 根据目标金币检查合理的耗时范围
    const expectedMinTime = this.getExpectedMinTime(targetCoins);
    if (timeMs < expectedMinTime) {
      errors.push(`完成${targetCoins}金币挑战的耗时过短，可能存在作弊`);
      suspiciousLevel = 'high';
    }

    // 检查是否有异常的性能提升
    if (previousBestTime !== undefined) {
      const improvement = previousBestTime - timeMs;
      const improvementRatio = improvement / previousBestTime;
      
      // 如果性能提升超过50%，标记为可疑
      if (improvementRatio > 0.5) {
        errors.push('性能提升异常，可能存在作弊');
        suspiciousLevel = 'high';
      }
    }

    // 检查耗时是否过于精确（可能是计算出来的而非真实游戏时间）
    if (timeMs % 1000 === 0 && timeMs < 60000) {
      // 小于1分钟且是整秒数，可疑
      suspiciousLevel = 'medium';
    }

    return {
      isValid: errors.length === 0,
      errors,
      suspiciousLevel,
    };
  }

  /**
   * 验证玩家基础信息
   * @param playerId 玩家ID
   * @param username 用户名
   * @param avatar 头像URL
   * @returns 验证结果
   */
  validatePlayerInfo(playerId: number, username: string, avatar?: string): ValidationResult {
    const errors: string[] = [];
    let suspiciousLevel: 'low' | 'medium' | 'high' = 'low';

    // 玩家ID检查
    if (playerId <= 0) {
      errors.push('玩家ID必须大于0');
      suspiciousLevel = 'high';
    }

    if (playerId > 999999999999) {
      errors.push('玩家ID超出范围');
      suspiciousLevel = 'high';
    }

    // 用户名检查
    if (!username || username.trim().length === 0) {
      errors.push('用户名不能为空');
      suspiciousLevel = 'high';
    }

    if (username.length > 50) {
      errors.push('用户名长度不能超过50字符');
      suspiciousLevel = 'medium';
    }

    // 检查用户名是否包含可疑内容
    const suspiciousPatterns = [
      /admin/i, /test/i, /bot/i, /hack/i, /cheat/i
    ];
    
    if (suspiciousPatterns.some(pattern => pattern.test(username))) {
      suspiciousLevel = 'medium';
    }

    // 头像URL检查
    if (avatar) {
      try {
        new URL(avatar);
      } catch {
        errors.push('头像URL格式无效');
        suspiciousLevel = 'medium';
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      suspiciousLevel,
    };
  }

  /**
   * 根据目标金币计算期望的最小完成时间
   * @param targetCoins 目标金币
   * @returns 最小耗时（毫秒）
   */
  private getExpectedMinTime(targetCoins: number): number {
    // 基于游戏机制设定的最小时间，防止不可能的快速完成
    const timeMap: Record<number, number> = {
      1000: 3000,      // 1K金币最少3秒
      10000: 15000,    // 1万金币最少15秒
      100000: 60000,   // 10万金币最少1分钟
      1000000: 300000, // 100万金币最少5分钟
      100000000: 1800000, // 1亿金币最少30分钟
    };

    return timeMap[targetCoins] || 1000; // 默认最少1秒
  }

  /**
   * 综合验证数据包
   * @param data 数据包
   * @returns 验证结果
   */
  validateDataPackage(data: any): ValidationResult {
    const errors: string[] = [];
    let suspiciousLevel: 'low' | 'medium' | 'high' = 'low';

    // 检查必要字段
    const requiredFields = ['id', 'username'];
    for (const field of requiredFields) {
      if (!(field in data)) {
        errors.push(`缺少必要字段: ${field}`);
        suspiciousLevel = 'high';
      }
    }

    // 检查数据类型
    if (typeof data.id !== 'number') {
      errors.push('玩家ID必须是数字');
      suspiciousLevel = 'high';
    }

    if (typeof data.username !== 'string') {
      errors.push('用户名必须是字符串');
      suspiciousLevel = 'high';
    }

    return {
      isValid: errors.length === 0,
      errors,
      suspiciousLevel,
    };
  }
}
