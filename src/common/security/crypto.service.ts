import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';

/**
 * 加密服务
 * 提供数据签名、验证等安全功能
 */
@Injectable()
export class CryptoService {
  private readonly secretKey: string;
  private readonly algorithm = 'sha256';

  constructor(private readonly configService: ConfigService) {
    this.secretKey = this.configService.get<string>('API_SECRET_KEY', 'default-secret-key-change-in-production');
  }

  /**
   * 生成数据签名
   * @param data 要签名的数据对象
   * @param timestamp 时间戳
   * @param nonce 随机数
   * @returns 签名字符串
   */
  generateSignature(data: any, timestamp: number, nonce: string): string {
    // 将数据按键名排序，确保签名一致性
    const sortedData = this.sortObjectKeys(data);
    
    // 构建签名字符串：timestamp + nonce + sortedData + secretKey
    const signString = `${timestamp}${nonce}${JSON.stringify(sortedData)}${this.secretKey}`;
    
    return crypto.createHash(this.algorithm).update(signString).digest('hex');
  }

  /**
   * 验证数据签名
   * @param data 数据对象
   * @param timestamp 时间戳
   * @param nonce 随机数
   * @param signature 客户端提供的签名
   * @returns 验证结果
   */
  verifySignature(data: any, timestamp: number, nonce: string, signature: string): boolean {
    const expectedSignature = this.generateSignature(data, timestamp, nonce);
    return expectedSignature === signature;
  }

  /**
   * 验证时间戳是否在有效范围内
   * @param timestamp 时间戳
   * @param toleranceMs 容忍的时间差（毫秒）
   * @returns 验证结果
   */
  verifyTimestamp(timestamp: number, toleranceMs: number = 300000): boolean { // 默认5分钟
    const now = Date.now();
    const diff = Math.abs(now - timestamp);
    return diff <= toleranceMs;
  }

  /**
   * 生成随机nonce
   * @param length nonce长度
   * @returns 随机字符串
   */
  generateNonce(length: number = 16): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 对象键名排序
   * @param obj 要排序的对象
   * @returns 排序后的对象
   */
  private sortObjectKeys(obj: any): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sortObjectKeys(item));
    }

    const sortedKeys = Object.keys(obj).sort();
    const sortedObj: any = {};
    
    for (const key of sortedKeys) {
      sortedObj[key] = this.sortObjectKeys(obj[key]);
    }
    
    return sortedObj;
  }

  /**
   * 生成玩家令牌（用于身份验证）
   * @param playerId 玩家ID
   * @param timestamp 时间戳
   * @returns 令牌
   */
  generatePlayerToken(playerId: number, timestamp: number): string {
    const tokenString = `${playerId}${timestamp}${this.secretKey}`;
    return crypto.createHash(this.algorithm).update(tokenString).digest('hex');
  }

  /**
   * 验证玩家令牌
   * @param playerId 玩家ID
   * @param timestamp 时间戳
   * @param token 客户端提供的令牌
   * @returns 验证结果
   */
  verifyPlayerToken(playerId: number, timestamp: number, token: string): boolean {
    const expectedToken = this.generatePlayerToken(playerId, timestamp);
    return expectedToken === token;
  }
}
