import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  BadRequestException,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { CryptoService } from './crypto.service';
import { AntiReplayService } from './anti-replay.service';
import { RateLimitService } from './rate-limit.service';
import { DataValidatorService } from './data-validator.service';

/**
 * 安全验证守卫
 * 统一处理签名验证、防重放、频率限制等安全检查
 */
@Injectable()
export class SecurityGuard implements CanActivate {
  private readonly logger = new Logger(SecurityGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly cryptoService: CryptoService,
    private readonly antiReplayService: AntiReplayService,
    private readonly rateLimitService: RateLimitService,
    private readonly dataValidatorService: DataValidatorService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    
    // 获取路由元数据
    const requireSignature = this.reflector.get<boolean>('requireSignature', context.getHandler()) ?? true;
    const rateLimitType = this.reflector.get<string>('rateLimitType', context.getHandler());
    const skipValidation = this.reflector.get<boolean>('skipValidation', context.getHandler()) ?? false;

    try {
      // 1. 数据签名验证
      if (requireSignature) {
        await this.verifySignature(request);
      }

      // 2. 频率限制检查
      if (rateLimitType) {
        await this.checkRateLimit(request, rateLimitType);
      }

      // 3. 数据合理性验证（只对POST请求进行数据验证）
      if (!skipValidation && request.method === 'POST' && request.body) {
        this.validateRequestData(request);
      }

      return true;
    } catch (error) {
      this.logger.warn(`安全检查失败: ${error.message}`, {
        ip: request.ip,
        userAgent: request.get('User-Agent'),
        path: request.path,
        method: request.method,
      });
      throw error;
    }
  }

  /**
   * 验证请求签名
   */
  private async verifySignature(request: Request): Promise<void> {
    const signature = request.headers['x-signature'] as string;
    const timestamp = parseInt(request.headers['x-timestamp'] as string);
    const nonce = request.headers['x-nonce'] as string;

    if (!signature || !timestamp || !nonce) {
      throw new UnauthorizedException('缺少必要的安全头信息');
    }

    // 验证时间戳
    if (!this.cryptoService.verifyTimestamp(timestamp)) {
      throw new UnauthorizedException('请求时间戳无效或已过期');
    }

    // 防重放攻击检查
    const nonceValid = await this.antiReplayService.checkAndRecordNonce(nonce);
    if (!nonceValid) {
      throw new UnauthorizedException('请求已被处理，疑似重放攻击');
    }

    // 验证签名
    const isValidSignature = this.cryptoService.verifySignature(
      request.body || {},
      timestamp,
      nonce,
      signature
    );

    if (!isValidSignature) {
      throw new UnauthorizedException('请求签名验证失败');
    }
  }

  /**
   * 检查频率限制
   */
  private async checkRateLimit(request: Request, rateLimitType: string): Promise<void> {
    // 使用IP地址作为限制键，也可以结合玩家ID
    const clientKey = request.ip;
    const playerId = request.body?.id;
    
    // 如果有玩家ID，使用更精确的限制键
    const limitKey = playerId ? `${clientKey}:${playerId}` : clientKey;

    const result = await this.rateLimitService.checkRateLimit(limitKey, rateLimitType);
    
    if (!result.allowed) {
      throw new HttpException({
        message: '请求过于频繁，请稍后再试',
        retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000),
        remaining: result.remaining,
      }, HttpStatus.TOO_MANY_REQUESTS);
    }

    // 在响应头中添加频率限制信息
    const response = request.res;
    if (response) {
      response.setHeader('X-RateLimit-Remaining', result.remaining);
      response.setHeader('X-RateLimit-Reset', result.resetTime);
    }
  }

  /**
   * 验证请求数据
   */
  private validateRequestData(request: Request): void {
    const data = request.body;
    
    // 基础数据包验证
    const packageValidation = this.dataValidatorService.validateDataPackage(data);
    if (!packageValidation.isValid) {
      throw new BadRequestException({
        message: '请求数据格式错误',
        errors: packageValidation.errors,
      });
    }

    // 根据请求路径进行特定验证
    if (request.path.includes('/coins/report')) {
      this.validateCoinsReport(data);
    } else if (request.path.includes('/time-challenge/report')) {
      this.validateTimeChallengeReport(data);
    }
  }

  /**
   * 验证金币上报数据
   */
  private validateCoinsReport(data: any): void {
    const validation = this.dataValidatorService.validateCoinsData(
      data.coins,
      data.id
    );

    if (!validation.isValid) {
      throw new BadRequestException({
        message: '金币数据验证失败',
        errors: validation.errors,
      });
    }

    if (validation.suspiciousLevel === 'high') {
      this.logger.warn(`检测到高风险金币数据`, {
        playerId: data.id,
        coins: data.coins,
        errors: validation.errors,
      });
      throw new BadRequestException('数据异常，请联系客服');
    }
  }

  /**
   * 验证耗时挑战上报数据
   */
  private validateTimeChallengeReport(data: any): void {
    const validation = this.dataValidatorService.validateTimeChallengeData(
      data.timeMs,
      data.targetCoins || 1000,
      data.id
    );

    if (!validation.isValid) {
      throw new BadRequestException({
        message: '耗时挑战数据验证失败',
        errors: validation.errors,
      });
    }

    if (validation.suspiciousLevel === 'high') {
      this.logger.warn(`检测到高风险耗时挑战数据`, {
        playerId: data.id,
        timeMs: data.timeMs,
        targetCoins: data.targetCoins,
        errors: validation.errors,
      });
      throw new BadRequestException('数据异常，请联系客服');
    }
  }
}
