import { SetMetadata } from '@nestjs/common';

/**
 * 要求签名验证装饰器
 * @param required 是否需要签名验证，默认true
 */
export const RequireSignature = (required: boolean = true) => 
  SetMetadata('requireSignature', required);

/**
 * 频率限制装饰器
 * @param type 限制类型
 */
export const RateLimit = (type: string) => 
  SetMetadata('rateLimitType', type);

/**
 * 跳过数据验证装饰器
 * @param skip 是否跳过验证，默认true
 */
export const SkipValidation = (skip: boolean = true) => 
  SetMetadata('skipValidation', skip);

/**
 * 安全级别装饰器
 * @param level 安全级别：low, medium, high
 */
export const SecurityLevel = (level: 'low' | 'medium' | 'high') => 
  SetMetadata('securityLevel', level);

/**
 * 组合装饰器：安全的数据上报接口
 * @param rateLimitType 频率限制类型
 */
export const SecureDataReport = (rateLimitType: string) => {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    RequireSignature(true)(target, propertyKey, descriptor);
    RateLimit(rateLimitType)(target, propertyKey, descriptor);
    SecurityLevel('high')(target, propertyKey, descriptor);
  };
};

/**
 * 组合装饰器：安全的查询接口
 * @param rateLimitType 频率限制类型
 */
export const SecureQuery = (rateLimitType: string) => {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    RequireSignature(false)(target, propertyKey, descriptor);
    RateLimit(rateLimitType)(target, propertyKey, descriptor);
    SecurityLevel('medium')(target, propertyKey, descriptor);
  };
};
