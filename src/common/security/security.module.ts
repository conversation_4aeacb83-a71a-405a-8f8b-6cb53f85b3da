import { Module, Global } from '@nestjs/common';
import { CryptoService } from './crypto.service';
import { AntiReplayService } from './anti-replay.service';
import { RateLimitService } from './rate-limit.service';
import { DataValidatorService } from './data-validator.service';
import { SecurityGuard } from './security.guard';
import { CacheModule } from '../cache/cache.module';

/**
 * 安全模块
 * 提供全局的安全服务
 */
@Global()
@Module({
  imports: [CacheModule], // 导入新的Redis缓存模块
  providers: [
    CryptoService,
    AntiReplayService,
    RateLimitService,
    DataValidatorService,
    SecurityGuard,
  ],
  exports: [
    CryptoService,
    AntiReplayService,
    RateLimitService,
    DataValidatorService,
    SecurityGuard,
  ],
})
export class SecurityModule {}
