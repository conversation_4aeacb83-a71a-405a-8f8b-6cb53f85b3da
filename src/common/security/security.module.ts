import { Module, Global } from '@nestjs/common';
import { CryptoService } from './crypto.service';
import { AntiReplayService } from './anti-replay.service';
import { RateLimitService } from './rate-limit.service';
import { DataValidatorService } from './data-validator.service';
import { SecurityGuard } from './security.guard';

/**
 * 安全模块
 * 提供全局的安全服务
 */
@Global()
@Module({
  providers: [
    CryptoService,
    AntiReplayService,
    RateLimitService,
    DataValidatorService,
    SecurityGuard,
  ],
  exports: [
    CryptoService,
    AntiReplayService,
    RateLimitService,
    DataValidatorService,
    SecurityGuard,
  ],
})
export class SecurityModule {}
