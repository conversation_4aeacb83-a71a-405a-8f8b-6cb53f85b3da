import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LeaderboardCache } from '../../entities/leaderboard-cache.entity';
import { PlayerModule } from '../player/player.module';
import { LeaderboardController } from './leaderboard.controller';
import { LeaderboardService } from './leaderboard.service';

/**
 * 排行榜模块
 * 封装排行榜相关的控制器、服务和实体
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([LeaderboardCache]),
    PlayerModule, // 导入玩家模块以使用 PlayerService
  ],
  controllers: [LeaderboardController],
  providers: [LeaderboardService],
  exports: [LeaderboardService], // 导出服务供其他模块使用
})
export class LeaderboardModule {}
