import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Leaderboard } from '../../entities/leaderboard.entity';
import { StatsModule } from '../stats/stats.module';
import { LeaderboardController } from './leaderboard.controller';
import { LeaderboardService } from './leaderboard.service';

/**
 * 排行榜模块
 * 封装排行榜相关的控制器、服务和实体
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([Leaderboard]),
    StatsModule, // 导入统计数据模块以使用 StatsService
  ],
  controllers: [LeaderboardController],
  providers: [LeaderboardService],
  exports: [LeaderboardService], // 导出服务供其他模块使用
})
export class LeaderboardModule {}
