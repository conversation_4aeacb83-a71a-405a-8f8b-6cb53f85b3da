import {
  Controller,
  Get,
  Post,
  Query,
  Param,
  HttpCode,
  HttpStatus,
  Logger,
  ParseIntPipe,
  BadRequestException,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { LeaderboardService } from './leaderboard.service';
import {
  LeaderboardType,
  getAllChallengeTargets,
  generateChallengeLeaderboardType
} from '../../common/enums/leaderboard-type.enum';
import { SecurityGuard } from '../../common/security/security.guard';
import { SecureQuery, RequireSignature, RateLimit } from '../../common/security/security.decorators';

/**
 * 排行榜控制器
 * 处理排行榜相关的HTTP请求
 */
@ApiTags('排行榜')
@Controller('api/leaderboard')
@UseGuards(SecurityGuard)
export class LeaderboardController {
  private readonly logger = new Logger(LeaderboardController.name);

  constructor(private readonly leaderboardService: LeaderboardService) {}

  /**
   * 验证排行榜类型是否有效
   */
  private isValidLeaderboardType(type: string): boolean {
    // 基础类型验证
    if (Object.values(LeaderboardType).includes(type as LeaderboardType)) {
      return true;
    }

    // 挑战类型验证
    if (type.startsWith('time_challenge_')) {
      const targetCoins = parseInt(type.replace('time_challenge_', ''));
      const challengeTargets = getAllChallengeTargets();
      return challengeTargets.some(target => target.coins === targetCoins);
    }

    return false;
  }

  /**
   * 获取可用的排行榜类型
   */
  @Get('types')
  @SecureQuery('leaderboard_query')
  @ApiOperation({
    summary: '获取排行榜类型',
    description: '获取所有可用的排行榜类型和配置信息',
  })
  @ApiResponse({
    status: 200,
    description: '获取排行榜类型成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            general: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  type: { type: 'string', example: 'coins' },
                  name: { type: 'string', example: '金币排行榜' },
                  description: { type: 'string', example: '按玩家金币数量排序' },
                  unit: { type: 'string', example: '金币' },
                },
              },
            },
            challenges: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  type: { type: 'string', example: 'time_challenge_1000' },
                  name: { type: 'string', example: '1K金币挑战' },
                  description: { type: 'string', example: '最快达到1000金币挑战' },
                  targetCoins: { type: 'number', example: 1000 },
                },
              },
            },
          },
        },
      },
    },
  })
  async getLeaderboardTypes() {
    this.logger.log('获取排行榜类型请求');

    try {
      const allTypes = this.leaderboardService.getAvailableLeaderboardTypes();
      const challengeTypes = this.leaderboardService.getChallengeLeaderboardTypes();

      // 分类返回
      const generalTypes = allTypes.filter(type => type.category === 'general');

      return {
        success: true,
        data: {
          general: generalTypes,
          challenges: challengeTypes,
        },
      };
    } catch (error) {
      this.logger.error(`获取排行榜类型失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取指定类型的排行榜数据接口
   */
  @Get(':type')
  @SecureQuery('leaderboard_query')
  @ApiOperation({
    summary: '获取指定类型排行榜',
    description: '获取指定类型的排行榜数据，优先从缓存获取以提高性能',
  })
  @ApiParam({
    name: 'type',
    description: '排行榜类型（如：coins, time_challenge_1000, time_challenge_10000）',
    example: 'coins',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: 'number',
    description: '返回的排行榜条数，默认100条，最大100条',
    example: 50,
  })
  @ApiResponse({
    status: 200,
    description: '获取排行榜成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            type: { type: 'string', example: 'coins' },
            leaderboard: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  rank: { type: 'number', example: 1 },
                  id: { type: 'number', example: 12345 },
                  username: { type: 'string', example: 'player001' },
                  avatar: { type: 'string', example: 'https://example.com/avatar.jpg' },
                  value: { type: 'number', example: 50000 },
                  displayText: { type: 'string', example: '50,000 金币' },
                },
              },
            },
            generatedAt: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
            totalPlayers: { type: 'number', example: 1000 },
          },
        },
      },
    },
  })
  async getLeaderboard(
    @Param('type') type: string,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number
  ) {
    this.logger.log(`获取${type}排行榜请求，限制条数: ${limit || '默认'}`);

    // 验证排行榜类型
    const isValidType = this.isValidLeaderboardType(type);
    if (!isValidType) {
      throw new BadRequestException(`不支持的排行榜类型: ${type}`);
    }

    try {
      const result = await this.leaderboardService.getLeaderboard(type, limit);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      this.logger.error(`获取${type}排行榜失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 手动刷新指定类型排行榜接口
   */
  @Post(':type/refresh')
  @HttpCode(HttpStatus.OK)
  @RequireSignature(false)
  @RateLimit('leaderboard_refresh')
  @ApiOperation({
    summary: '手动刷新指定类型排行榜',
    description: '手动触发指定类型排行榜数据的重新生成和缓存更新',
  })
  @ApiParam({
    name: 'type',
    description: '排行榜类型（如：coins, time_challenge_1000, time_challenge_10000）',
    example: 'coins',
  })
  @ApiResponse({
    status: 200,
    description: '排行榜刷新成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '金币排行榜刷新任务已启动' },
        taskId: { type: 'string', example: 'refresh_coins_20240101_120000' },
      },
    },
  })
  async refreshLeaderboard(@Param('type') type: string) {
    // 验证排行榜类型
    const isValidType = this.isValidLeaderboardType(type);
    if (!isValidType) {
      throw new BadRequestException(`不支持的排行榜类型: ${type}`);
    }

    const taskId = `refresh_${type}_${new Date().toISOString().replace(/[-:]/g, '').split('.')[0]}`;
    this.logger.log(`手动刷新${type}排行榜请求，任务ID: ${taskId}`);

    try {
      // 异步执行排行榜生成，不阻塞响应
      setImmediate(async () => {
        try {
          await this.leaderboardService.generateLeaderboard(type);
          this.logger.log(`${type}排行榜刷新任务完成: ${taskId}`);
        } catch (error) {
          this.logger.error(`${type}排行榜刷新任务失败: ${taskId}, 错误: ${error.message}`, error.stack);
        }
      });

      return {
        success: true,
        message: `${type}排行榜刷新任务已启动`,
        taskId,
      };
    } catch (error) {
      this.logger.error(`启动${type}排行榜刷新任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 手动刷新所有排行榜接口
   */
  @Post('refresh-all')
  @HttpCode(HttpStatus.OK)
  @RequireSignature(false)
  @RateLimit('leaderboard_refresh')
  @ApiOperation({
    summary: '手动刷新所有排行榜',
    description: '手动触发所有类型排行榜数据的重新生成和缓存更新',
  })
  @ApiResponse({
    status: 200,
    description: '所有排行榜刷新成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '所有排行榜刷新任务已启动' },
        taskId: { type: 'string', example: 'refresh_all_20240101_120000' },
      },
    },
  })
  async refreshAllLeaderboards() {
    const taskId = `refresh_all_${new Date().toISOString().replace(/[-:]/g, '').split('.')[0]}`;
    this.logger.log(`手动刷新所有排行榜请求，任务ID: ${taskId}`);

    try {
      // 异步执行所有排行榜生成，不阻塞响应
      setImmediate(async () => {
        try {
          await this.leaderboardService.generateAllLeaderboards();
          this.logger.log(`所有排行榜刷新任务完成: ${taskId}`);
        } catch (error) {
          this.logger.error(`所有排行榜刷新任务失败: ${taskId}, 错误: ${error.message}`, error.stack);
        }
      });

      return {
        success: true,
        message: '所有排行榜刷新任务已启动',
        taskId,
      };
    } catch (error) {
      this.logger.error(`启动所有排行榜刷新任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取所有挑战目标
   */
  @Get('challenges')
  @SecureQuery('leaderboard_query')
  @ApiOperation({
    summary: '获取挑战目标',
    description: '获取所有可用的挑战目标配置',
  })
  @ApiResponse({
    status: 200,
    description: '获取挑战目标成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              coins: { type: 'number', example: 1000 },
              name: { type: 'string', example: 'challenge_1k' },
              displayName: { type: 'string', example: '1K金币挑战' },
              description: { type: 'string', example: '最快达到1000金币挑战' },
              leaderboardType: { type: 'string', example: 'time_challenge_1000' },
            },
          },
        },
      },
    },
  })
  async getChallengeTargets() {
    this.logger.log('获取挑战目标请求');

    try {
      const challengeTargets = getAllChallengeTargets();
      const data = challengeTargets.map(target => ({
        coins: target.coins,
        name: target.name,
        displayName: target.displayName,
        description: target.description,
        leaderboardType: generateChallengeLeaderboardType(target.coins),
      }));

      return {
        success: true,
        data,
      };
    } catch (error) {
      this.logger.error(`获取挑战目标失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
