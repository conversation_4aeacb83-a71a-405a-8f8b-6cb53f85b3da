import {
  Controller,
  Get,
  Post,
  Query,
  HttpCode,
  HttpStatus,
  Logger,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { LeaderboardService } from './leaderboard.service';

/**
 * 排行榜控制器
 * 处理排行榜相关的HTTP请求
 */
@ApiTags('排行榜')
@Controller('api/leaderboard')
export class LeaderboardController {
  private readonly logger = new Logger(LeaderboardController.name);

  constructor(private readonly leaderboardService: LeaderboardService) {}

  /**
   * 获取排行榜数据接口
   */
  @Get()
  @ApiOperation({
    summary: '获取排行榜',
    description: '获取当前的排行榜数据，优先从缓存获取以提高性能',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: 'number',
    description: '返回的排行榜条数，默认100条，最大100条',
    example: 50,
  })
  @ApiResponse({
    status: 200,
    description: '获取排行榜成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            leaderboard: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  rank: { type: 'number', example: 1 },
                  id: { type: 'number', example: 12345 },
                  username: { type: 'string', example: 'player001' },
                  avatar: { type: 'string', example: 'https://example.com/avatar.jpg' },
                  coins: { type: 'number', example: 50000 },
                },
              },
            },
            generatedAt: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
            totalPlayers: { type: 'number', example: 1000 },
          },
        },
      },
    },
  })
  async getLeaderboard(@Query('limit', new ParseIntPipe({ optional: true })) limit?: number) {
    this.logger.log(`获取排行榜请求，限制条数: ${limit || '默认'}`);

    try {
      const result = await this.leaderboardService.getLeaderboard(limit);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      this.logger.error(`获取排行榜失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 手动刷新排行榜接口
   */
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '手动刷新排行榜',
    description: '手动触发排行榜数据的重新生成和缓存更新',
  })
  @ApiResponse({
    status: 200,
    description: '排行榜刷新成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '排行榜刷新成功' },
        taskId: { type: 'string', example: 'refresh_20240101_120000' },
      },
    },
  })
  async refreshLeaderboard() {
    const taskId = `refresh_${new Date().toISOString().replace(/[-:]/g, '').split('.')[0]}`;
    this.logger.log(`手动刷新排行榜请求，任务ID: ${taskId}`);

    try {
      // 异步执行排行榜生成，不阻塞响应
      setImmediate(async () => {
        try {
          await this.leaderboardService.generateLeaderboard();
          this.logger.log(`排行榜刷新任务完成: ${taskId}`);
        } catch (error) {
          this.logger.error(`排行榜刷新任务失败: ${taskId}, 错误: ${error.message}`, error.stack);
        }
      });

      return {
        success: true,
        message: '排行榜刷新任务已启动',
        taskId,
      };
    } catch (error) {
      this.logger.error(`启动排行榜刷新任务失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
