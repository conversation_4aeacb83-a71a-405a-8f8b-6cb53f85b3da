import { Injectable, Logger, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
import { LeaderboardCache } from '../../entities/leaderboard-cache.entity';
import { PlayerService } from '../player/player.service';

/**
 * 排行榜服务
 * 处理排行榜相关的业务逻辑
 */
@Injectable()
export class LeaderboardService {
  private readonly logger = new Logger(LeaderboardService.name);
  private readonly CACHE_KEY = 'leaderboard:top';
  private readonly leaderboardSize: number;

  constructor(
    @InjectRepository(LeaderboardCache)
    private readonly leaderboardRepository: Repository<LeaderboardCache>,
    private readonly playerService: PlayerService,
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
    private readonly configService: ConfigService,
  ) {
    this.leaderboardSize = this.configService.get<number>('LEADERBOARD_SIZE', 100);
  }

  /**
   * 获取排行榜数据
   * 优先从缓存获取，缓存不存在时从数据库获取
   */
  async getLeaderboard(limit?: number) {
    const actualLimit = Math.min(limit || this.leaderboardSize, this.leaderboardSize);
    
    try {
      // 尝试从 Redis 缓存获取
      const cachedData = await this.cacheManager.get(this.CACHE_KEY);
      if (cachedData) {
        this.logger.log('从缓存获取排行榜数据');
        const data = JSON.parse(cachedData as string);
        return {
          leaderboard: data.leaderboard.slice(0, actualLimit),
          generatedAt: data.generatedAt,
          totalPlayers: data.totalPlayers,
          fromCache: true,
        };
      }

      // 缓存不存在，从数据库获取
      this.logger.log('缓存不存在，从数据库获取排行榜数据');
      return await this.getLeaderboardFromDatabase(actualLimit);
    } catch (error) {
      this.logger.error(`获取排行榜失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 从数据库获取排行榜数据
   */
  private async getLeaderboardFromDatabase(limit: number) {
    const leaderboardData = await this.leaderboardRepository.find({
      order: { rankPosition: 'ASC' },
      take: limit,
    });

    if (leaderboardData.length === 0) {
      this.logger.warn('数据库中没有排行榜数据，可能需要先生成排行榜');
      return {
        leaderboard: [],
        generatedAt: null,
        totalPlayers: 0,
        fromCache: false,
      };
    }

    const totalPlayers = await this.playerService.getPlayerCount();
    const generatedAt = leaderboardData[0]?.generatedAt;

    const formattedData = leaderboardData.map(item => ({
      rank: item.rankPosition,
      id: item.playerId,
      username: item.username,
      avatar: item.avatar,
      coins: item.coins,
    }));

    return {
      leaderboard: formattedData,
      generatedAt,
      totalPlayers,
      fromCache: false,
    };
  }

  /**
   * 生成排行榜
   * 从玩家表获取数据，排序后存储到排行榜缓存表和Redis缓存
   */
  async generateLeaderboard(): Promise<void> {
    this.logger.log('开始生成排行榜...');
    
    try {
      // 获取按金币排序的玩家数据
      const topPlayers = await this.playerService.getTopPlayersByCoins(this.leaderboardSize);
      
      if (topPlayers.length === 0) {
        this.logger.warn('没有玩家数据，跳过排行榜生成');
        return;
      }

      // 清空旧的排行榜缓存数据
      await this.leaderboardRepository.clear();
      this.logger.log('已清空旧的排行榜缓存数据');

      // 生成新的排行榜数据
      const leaderboardEntries = topPlayers.map((player, index) => {
        const entry = new LeaderboardCache();
        entry.rankPosition = index + 1;
        entry.playerId = player.id;
        entry.username = player.username;
        entry.avatar = player.avatar;
        entry.coins = player.coins;
        return entry;
      });

      // 批量插入到数据库
      await this.leaderboardRepository.save(leaderboardEntries);
      this.logger.log(`已生成 ${leaderboardEntries.length} 条排行榜记录`);

      // 更新 Redis 缓存
      await this.updateCache(leaderboardEntries);
      
      this.logger.log('排行榜生成完成');
    } catch (error) {
      this.logger.error(`生成排行榜失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新 Redis 缓存
   */
  private async updateCache(leaderboardEntries: LeaderboardCache[]): Promise<void> {
    try {
      const totalPlayers = await this.playerService.getPlayerCount();
      const cacheData = {
        leaderboard: leaderboardEntries.map(entry => ({
          rank: entry.rankPosition,
          id: entry.playerId,
          username: entry.username,
          avatar: entry.avatar,
          coins: entry.coins,
        })),
        generatedAt: new Date(),
        totalPlayers,
      };

      const ttl = this.configService.get<number>('LEADERBOARD_CACHE_TTL', 86400) * 1000;
      await this.cacheManager.set(this.CACHE_KEY, JSON.stringify(cacheData), ttl);
      
      this.logger.log('Redis 缓存已更新');
    } catch (error) {
      this.logger.error(`更新缓存失败: ${error.message}`, error.stack);
      // 缓存更新失败不影响主流程
    }
  }

  /**
   * 清除缓存
   */
  async clearCache(): Promise<void> {
    try {
      await this.cacheManager.del(this.CACHE_KEY);
      this.logger.log('排行榜缓存已清除');
    } catch (error) {
      this.logger.error(`清除缓存失败: ${error.message}`, error.stack);
    }
  }
}
