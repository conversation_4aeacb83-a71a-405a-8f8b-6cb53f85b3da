import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { RedisCacheService } from '../../common/cache/redis-cache.service';
import { Leaderboard } from '../../entities/leaderboard.entity';
import {
  LeaderboardType,
  getLeaderboardConfig,
  getAllChallengeTargets,
  generateChallengeLeaderboardType
} from '../../common/enums/leaderboard-type.enum';
import { StatsService } from '../stats/stats.service';

/**
 * 排行榜服务
 * 处理排行榜相关的业务逻辑
 */
@Injectable()
export class LeaderboardService {
  private readonly logger = new Logger(LeaderboardService.name);
  private readonly leaderboardSize: number;

  constructor(
    @InjectRepository(Leaderboard)
    private readonly leaderboardRepository: Repository<Leaderboard>,
    private readonly statsService: StatsService,
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache,
    private readonly configService: ConfigService,
  ) {
    this.leaderboardSize = this.configService.get<number>('LEADERBOARD_SIZE', 100);
  }

  /**
   * 生成缓存键
   */
  private getCacheKey(leaderboardType: string): string {
    return `leaderboard:${leaderboardType}`;
  }

  /**
   * 获取排行榜数据
   * 优先从缓存获取，缓存不存在时从数据库获取
   */
  async getLeaderboard(leaderboardType: string, limit?: number) {
    const actualLimit = Math.min(limit || this.leaderboardSize, this.leaderboardSize);
    const cacheKey = this.getCacheKey(leaderboardType);

    try {
      // 尝试从 Redis 缓存获取
      const cachedData = await this.cacheManager.get(cacheKey);
      if (cachedData) {
        this.logger.log(`从缓存获取${leaderboardType}排行榜数据`);
        const data = JSON.parse(cachedData as string);
        return {
          type: leaderboardType,
          leaderboard: data.leaderboard.slice(0, actualLimit),
          generatedAt: data.generatedAt,
          totalPlayers: data.totalPlayers,
          fromCache: true,
        };
      }

      // 缓存不存在，从数据库获取
      this.logger.log(`缓存不存在，从数据库获取${leaderboardType}排行榜数据`);
      return await this.getLeaderboardFromDatabase(leaderboardType, actualLimit);
    } catch (error) {
      this.logger.error(`获取${leaderboardType}排行榜失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 从数据库获取排行榜数据
   */
  private async getLeaderboardFromDatabase(leaderboardType: string, limit: number) {
    const leaderboardData = await this.leaderboardRepository.find({
      where: { leaderboardType },
      order: { rankPosition: 'ASC' },
      take: limit,
    });

    if (leaderboardData.length === 0) {
      this.logger.warn(`数据库中没有${leaderboardType}排行榜数据，可能需要先生成排行榜`);
      return {
        type: leaderboardType,
        leaderboard: [],
        generatedAt: null,
        totalPlayers: 0,
        fromCache: false,
      };
    }

    const totalPlayers = await this.statsService.getPlayerCountByType(leaderboardType);
    const generatedAt = leaderboardData[0]?.generatedAt;
    const config = getLeaderboardConfig(leaderboardType);

    const formattedData = leaderboardData.map(item => ({
      rank: item.rankPosition,
      id: item.playerId,
      username: item.username,
      avatar: item.avatar,
      value: item.statValue,
      displayText: item.displayText || config.formatValue(item.statValue),
    }));

    return {
      type: leaderboardType,
      leaderboard: formattedData,
      generatedAt,
      totalPlayers,
      fromCache: false,
    };
  }

  /**
   * 生成指定类型的排行榜
   */
  async generateLeaderboard(leaderboardType: string): Promise<void> {
    this.logger.log(`开始生成${leaderboardType}排行榜...`);

    try {
      const config = getLeaderboardConfig(leaderboardType);

      // 获取排序后的统计数据
      const topStats = await this.statsService.getTopStatsByType(
        leaderboardType,
        this.leaderboardSize,
        config.sortOrder
      );

      if (topStats.length === 0) {
        this.logger.warn(`没有${leaderboardType}统计数据，跳过排行榜生成`);
        return;
      }

      // 清空旧的排行榜缓存数据
      await this.leaderboardRepository.delete({ leaderboardType });
      this.logger.log(`已清空旧的${leaderboardType}排行榜缓存数据`);

      // 生成新的排行榜数据
      const leaderboardEntries = topStats.map((stats, index) => {
        const entry = new Leaderboard();
        entry.leaderboardType = leaderboardType;
        entry.rankPosition = index + 1;
        entry.playerId = stats.playerId;
        entry.username = stats.username;
        entry.avatar = stats.avatar;
        entry.statValue = stats.statValue;
        entry.displayText = config.formatValue(stats.statValue);
        return entry;
      });

      // 批量插入到数据库
      await this.leaderboardRepository.save(leaderboardEntries);
      this.logger.log(`已生成 ${leaderboardEntries.length} 条${leaderboardType}排行榜记录`);

      // 更新 Redis 缓存
      await this.updateCache(leaderboardType, leaderboardEntries);

      this.logger.log(`${leaderboardType}排行榜生成完成`);
    } catch (error) {
      this.logger.error(`生成${leaderboardType}排行榜失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新 Redis 缓存
   */
  private async updateCache(leaderboardType: string, leaderboardEntries: Leaderboard[]): Promise<void> {
    try {
      const totalPlayers = await this.statsService.getPlayerCountByType(leaderboardType);
      const config = getLeaderboardConfig(leaderboardType);
      const cacheKey = this.getCacheKey(leaderboardType);

      const cacheData = {
        leaderboard: leaderboardEntries.map(entry => ({
          rank: entry.rankPosition,
          id: entry.playerId,
          username: entry.username,
          avatar: entry.avatar,
          value: entry.statValue,
          displayText: entry.displayText,
        })),
        generatedAt: new Date(),
        totalPlayers,
      };

      const ttl = this.configService.get<number>('LEADERBOARD_CACHE_TTL', 86400) * 1000;
      await this.cacheManager.set(cacheKey, JSON.stringify(cacheData), ttl);

      this.logger.log(`${leaderboardType} Redis 缓存已更新`);
    } catch (error) {
      this.logger.error(`更新${leaderboardType}缓存失败: ${error.message}`, error.stack);
      // 缓存更新失败不影响主流程
    }
  }

  /**
   * 清除指定类型的缓存
   */
  async clearCache(leaderboardType: string): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(leaderboardType);
      await this.cacheManager.del(cacheKey);
      this.logger.log(`${leaderboardType}排行榜缓存已清除`);
    } catch (error) {
      this.logger.error(`清除${leaderboardType}缓存失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 生成所有类型的排行榜
   */
  async generateAllLeaderboards(): Promise<void> {
    this.logger.log('开始生成所有类型的排行榜...');

    const allStatTypes = this.statsService.getAllStatTypes();

    for (const type of allStatTypes) {
      try {
        await this.generateLeaderboard(type);
      } catch (error) {
        this.logger.error(`生成${type}排行榜失败: ${error.message}`, error.stack);
        // 继续生成其他类型的排行榜
      }
    }

    this.logger.log('所有排行榜生成完成');
  }

  /**
   * 获取所有可用的排行榜类型
   */
  getAvailableLeaderboardTypes(): Array<{
    type: string;
    name: string;
    description: string;
    unit: string;
    category?: string;
  }> {
    const allStatTypes = this.statsService.getAllStatTypes();

    return allStatTypes.map(type => {
      const config = getLeaderboardConfig(type);

      // 判断是否为挑战类型
      const isChallenge = type.startsWith('time_challenge_');

      return {
        type,
        name: config.name,
        description: config.description,
        unit: config.unit,
        category: isChallenge ? 'challenge' : 'general',
      };
    });
  }

  /**
   * 获取所有挑战排行榜类型
   */
  getChallengeLeaderboardTypes(): Array<{
    type: string;
    name: string;
    description: string;
    targetCoins: number;
  }> {
    const challengeTargets = getAllChallengeTargets();

    return challengeTargets.map(target => ({
      type: generateChallengeLeaderboardType(target.coins),
      name: target.displayName,
      description: target.description,
      targetCoins: target.coins,
    }));
  }
}
