import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { LeaderboardModule } from '../leaderboard/leaderboard.module';
import { SchedulerService } from './scheduler.service';

/**
 * 调度模块
 * 封装定时任务相关的服务
 */
@Module({
  imports: [
    ScheduleModule.forRoot(), // 启用定时任务功能
    LeaderboardModule, // 导入排行榜模块以使用 LeaderboardService
  ],
  providers: [SchedulerService],
  exports: [SchedulerService],
})
export class SchedulerModule {}
