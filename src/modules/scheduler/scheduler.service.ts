import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import { LeaderboardService } from '../leaderboard/leaderboard.service';

/**
 * 调度服务
 * 处理定时任务，如定时更新排行榜
 */
@Injectable()
export class SchedulerService {
  private readonly logger = new Logger(SchedulerService.name);
  private readonly cronExpression: string;

  constructor(
    private readonly leaderboardService: LeaderboardService,
    private readonly configService: ConfigService,
  ) {
    // 从环境变量获取 Cron 表达式，默认每日凌晨0点
    this.cronExpression = this.configService.get<string>(
      'LEADERBOARD_UPDATE_CRON',
      '0 0 0 * * *'
    );
    
    this.logger.log(`排行榜定时更新任务已配置，Cron表达式: ${this.cronExpression}`);
  }

  /**
   * 定时更新排行榜任务
   * 默认每日凌晨0点执行
   */
  @Cron('0 0 0 * * *', {
    name: 'updateLeaderboard',
    timeZone: 'Asia/Shanghai',
  })
  async handleLeaderboardUpdate() {
    const taskId = `scheduled_${new Date().toISOString().replace(/[-:]/g, '').split('.')[0]}`;
    this.logger.log(`开始执行定时排行榜更新任务: ${taskId}`);

    try {
      const startTime = Date.now();
      
      // 执行排行榜生成
      await this.leaderboardService.generateLeaderboard();
      
      const duration = Date.now() - startTime;
      this.logger.log(`定时排行榜更新任务完成: ${taskId}, 耗时: ${duration}ms`);
    } catch (error) {
      this.logger.error(
        `定时排行榜更新任务失败: ${taskId}, 错误: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * 每小时清理过期数据（可选）
   * 清理超过7天的排行榜缓存记录
   */
  @Cron(CronExpression.EVERY_HOUR, {
    name: 'cleanupOldData',
    timeZone: 'Asia/Shanghai',
  })
  async handleDataCleanup() {
    this.logger.log('开始执行数据清理任务');

    try {
      // 这里可以添加清理逻辑，比如删除过期的排行榜缓存记录
      // 由于我们的设计是每次生成排行榜时清空旧数据，所以这个任务可以用于其他清理工作
      
      this.logger.log('数据清理任务完成');
    } catch (error) {
      this.logger.error(`数据清理任务失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取定时任务状态信息
   */
  getSchedulerInfo() {
    return {
      leaderboardUpdateCron: this.cronExpression,
      timezone: 'Asia/Shanghai',
      tasks: [
        {
          name: 'updateLeaderboard',
          description: '定时更新排行榜',
          cron: '0 0 0 * * *',
          nextRun: this.getNextRunTime('0 0 0 * * *'),
        },
        {
          name: 'cleanupOldData',
          description: '清理过期数据',
          cron: CronExpression.EVERY_HOUR,
          nextRun: this.getNextRunTime(CronExpression.EVERY_HOUR),
        },
      ],
    };
  }

  /**
   * 计算下次执行时间（简化版本）
   */
  private getNextRunTime(cronExpression: string): string {
    // 这里可以使用 cron-parser 库来精确计算下次执行时间
    // 为了简化，这里返回一个示例时间
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    return tomorrow.toISOString();
  }
}
