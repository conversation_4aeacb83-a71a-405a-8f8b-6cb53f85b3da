import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PlayerStats } from '../../entities/player-stats.entity';
import { StatsController } from './stats.controller';
import { StatsService } from './stats.service';

/**
 * 统计数据模块
 * 封装统计数据相关的控制器、服务和实体
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([PlayerStats]),
  ],
  controllers: [StatsController],
  providers: [StatsService],
  exports: [StatsService], // 导出服务供其他模块使用
})
export class StatsModule {}
