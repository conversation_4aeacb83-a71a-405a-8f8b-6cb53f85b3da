import { <PERSON>N<PERSON><PERSON>, IsString, <PERSON><PERSON>ptional, <PERSON>U<PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

/**
 * 上报金币统计数据 DTO
 */
export class ReportCoinsStatsDto {
  /**
   * 玩家ID
   */
  @ApiProperty({
    description: '玩家ID',
    example: 12345,
    type: 'number',
  })
  @IsNumber({}, { message: '玩家ID必须是数字' })
  @Min(1, { message: '玩家ID必须大于0' })
  @Transform(({ value }) => parseInt(value))
  id: number;

  /**
   * 用户名
   */
  @ApiProperty({
    description: '用户名',
    example: 'player001',
    maxLength: 50,
  })
  @IsString({ message: '用户名必须是字符串' })
  @Transform(({ value }) => value?.trim())
  username: string;

  /**
   * 头像URL (可选)
   */
  @ApiProperty({
    description: '头像URL',
    example: 'https://example.com/avatar.jpg',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsUrl({}, { message: '头像必须是有效的URL' })
  avatar?: string;

  /**
   * 金币数量
   */
  @ApiProperty({
    description: '金币数量',
    example: 10000,
    minimum: 0,
    maximum: 999999999999,
  })
  @IsNumber({}, { message: '金币数量必须是数字' })
  @Min(0, { message: '金币数量不能为负数' })
  @Max(999999999999, { message: '金币数量超出最大限制' })
  @Transform(({ value }) => parseInt(value))
  coins: number;
}
