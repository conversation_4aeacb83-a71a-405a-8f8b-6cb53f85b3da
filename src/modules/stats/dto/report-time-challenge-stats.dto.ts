import { <PERSON>N<PERSON><PERSON>, IsString, IsOptional, IsU<PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

/**
 * 上报耗时挑战统计数据 DTO
 */
export class ReportTimeChallengeStatsDto {
  /**
   * 玩家ID
   */
  @ApiProperty({
    description: '玩家ID',
    example: 12345,
    type: 'number',
  })
  @IsNumber({}, { message: '玩家ID必须是数字' })
  @Min(1, { message: '玩家ID必须大于0' })
  @Transform(({ value }) => parseInt(value))
  id: number;

  /**
   * 用户名
   */
  @ApiProperty({
    description: '用户名',
    example: 'player001',
    maxLength: 50,
  })
  @IsString({ message: '用户名必须是字符串' })
  @Transform(({ value }) => value?.trim())
  username: string;

  /**
   * 头像URL (可选)
   */
  @ApiProperty({
    description: '头像URL',
    example: 'https://example.com/avatar.jpg',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsUrl({}, { message: '头像必须是有效的URL' })
  avatar?: string;

  /**
   * 完成挑战耗时（毫秒）
   */
  @ApiProperty({
    description: '完成固定金币挑战的耗时（毫秒）',
    example: 15500,
    minimum: 1,
    maximum: 3600000, // 最大1小时
  })
  @IsNumber({}, { message: '耗时必须是数字' })
  @Min(1, { message: '耗时必须大于0毫秒' })
  @Max(3600000, { message: '耗时不能超过1小时' })
  @Transform(({ value }) => parseInt(value))
  timeMs: number;

  /**
   * 挑战目标金币数量（可选，用于记录）
   */
  @ApiProperty({
    description: '挑战目标金币数量',
    example: 1000,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: '目标金币数量必须是数字' })
  @Min(1, { message: '目标金币数量必须大于0' })
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  targetCoins?: number;
}
