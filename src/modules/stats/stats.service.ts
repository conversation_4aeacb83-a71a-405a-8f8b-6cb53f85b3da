import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PlayerStats } from '../../entities/player-stats.entity';
import { LeaderboardType } from '../../common/enums/leaderboard-type.enum';
import { ReportCoinsStatsDto } from './dto/report-coins-stats.dto';
import { ReportTimeChallengeStatsDto } from './dto/report-time-challenge-stats.dto';

/**
 * 统计数据服务
 * 处理各种类型的玩家统计数据
 */
@Injectable()
export class StatsService {
  private readonly logger = new Logger(StatsService.name);

  constructor(
    @InjectRepository(PlayerStats)
    private readonly playerStatsRepository: Repository<PlayerStats>,
  ) {}

  /**
   * 上报金币统计数据
   */
  async reportCoinsStats(reportDto: ReportCoinsStatsDto): Promise<PlayerStats> {
    this.logger.log(`上报金币统计数据: ID=${reportDto.id}, 用户名=${reportDto.username}, 金币=${reportDto.coins}`);

    try {
      // 查找现有记录
      let playerStats = await this.playerStatsRepository.findOne({
        where: {
          playerId: reportDto.id,
          statType: LeaderboardType.COINS,
        },
      });

      if (playerStats) {
        // 更新现有记录
        playerStats.username = reportDto.username;
        playerStats.avatar = reportDto.avatar;
        playerStats.statValue = reportDto.coins;
      } else {
        // 创建新记录
        playerStats = this.playerStatsRepository.create({
          playerId: reportDto.id,
          username: reportDto.username,
          avatar: reportDto.avatar,
          statType: LeaderboardType.COINS,
          statValue: reportDto.coins,
        });
      }

      const savedStats = await this.playerStatsRepository.save(playerStats);
      this.logger.log(`金币统计数据上报成功: ID=${savedStats.playerId}`);
      return savedStats;
    } catch (error) {
      this.logger.error(`金币统计数据上报失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 上报耗时挑战统计数据
   */
  async reportTimeChallengeStats(reportDto: ReportTimeChallengeStatsDto): Promise<PlayerStats> {
    this.logger.log(`上报耗时挑战统计数据: ID=${reportDto.id}, 用户名=${reportDto.username}, 耗时=${reportDto.timeMs}ms`);

    try {
      // 查找现有记录
      let playerStats = await this.playerStatsRepository.findOne({
        where: {
          playerId: reportDto.id,
          statType: LeaderboardType.TIME_CHALLENGE,
        },
      });

      // 准备额外数据
      const extraData = {
        targetCoins: reportDto.targetCoins,
      };

      if (playerStats) {
        // 只有当新的耗时更短时才更新记录
        if (reportDto.timeMs < playerStats.statValue) {
          playerStats.username = reportDto.username;
          playerStats.avatar = reportDto.avatar;
          playerStats.statValue = reportDto.timeMs;
          playerStats.extraData = extraData;
          this.logger.log(`更新耗时记录: 从 ${playerStats.statValue}ms 提升到 ${reportDto.timeMs}ms`);
        } else {
          this.logger.log(`耗时记录未更新: 当前最佳 ${playerStats.statValue}ms, 本次 ${reportDto.timeMs}ms`);
          return playerStats;
        }
      } else {
        // 创建新记录
        playerStats = this.playerStatsRepository.create({
          playerId: reportDto.id,
          username: reportDto.username,
          avatar: reportDto.avatar,
          statType: LeaderboardType.TIME_CHALLENGE,
          statValue: reportDto.timeMs,
          extraData,
        });
      }

      const savedStats = await this.playerStatsRepository.save(playerStats);
      this.logger.log(`耗时挑战统计数据上报成功: ID=${savedStats.playerId}`);
      return savedStats;
    } catch (error) {
      this.logger.error(`耗时挑战统计数据上报失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取指定类型的排行榜数据（用于生成排行榜）
   */
  async getTopStatsByType(
    statType: LeaderboardType,
    limit: number = 100,
    sortOrder: 'ASC' | 'DESC' = 'DESC'
  ): Promise<PlayerStats[]> {
    return this.playerStatsRepository.find({
      where: { statType },
      order: { 
        statValue: sortOrder,
        updatedAt: 'DESC' // 相同数值时按更新时间排序
      },
      take: limit,
    });
  }

  /**
   * 获取玩家总数（按统计类型）
   */
  async getPlayerCountByType(statType: LeaderboardType): Promise<number> {
    return this.playerStatsRepository.count({
      where: { statType },
    });
  }

  /**
   * 根据玩家ID和统计类型查找记录
   */
  async findPlayerStatsByType(playerId: number, statType: LeaderboardType): Promise<PlayerStats | null> {
    return this.playerStatsRepository.findOne({
      where: { playerId, statType },
    });
  }
}
