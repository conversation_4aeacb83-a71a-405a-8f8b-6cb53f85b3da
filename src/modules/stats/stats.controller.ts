import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
} from '@nestjs/swagger';
import { StatsService } from './stats.service';
import { ReportCoinsStatsDto } from './dto/report-coins-stats.dto';
import { ReportTimeChallengeStatsDto } from './dto/report-time-challenge-stats.dto';

/**
 * 统计数据控制器
 * 处理各种类型的统计数据上报
 */
@ApiTags('统计数据')
@Controller('api/stats')
export class StatsController {
  private readonly logger = new Logger(StatsController.name);

  constructor(private readonly statsService: StatsService) {}

  /**
   * 上报金币统计数据接口
   */
  @Post('coins/report')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '上报金币统计数据',
    description: '客户端调用此接口上报玩家的金币统计数据',
  })
  @ApiBody({
    type: ReportCoinsStatsDto,
    description: '金币统计数据',
  })
  @ApiResponse({
    status: 200,
    description: '金币数据上报成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '金币数据上报成功' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 12345 },
            username: { type: 'string', example: 'player001' },
            avatar: { type: 'string', example: 'https://example.com/avatar.jpg' },
            coins: { type: 'number', example: 10000 },
            updatedAt: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
          },
        },
      },
    },
  })
  async reportCoinsStats(@Body() reportDto: ReportCoinsStatsDto) {
    this.logger.log(`接收到金币统计数据上报请求: ${JSON.stringify(reportDto)}`);

    try {
      const playerStats = await this.statsService.reportCoinsStats(reportDto);

      return {
        success: true,
        message: '金币数据上报成功',
        data: {
          id: playerStats.playerId,
          username: playerStats.username,
          avatar: playerStats.avatar,
          coins: playerStats.statValue,
          updatedAt: playerStats.updatedAt,
        },
      };
    } catch (error) {
      this.logger.error(`金币统计数据上报失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 上报耗时挑战统计数据接口
   */
  @Post('time-challenge/report')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '上报耗时挑战统计数据',
    description: '客户端调用此接口上报玩家完成固定金币挑战的耗时数据',
  })
  @ApiBody({
    type: ReportTimeChallengeStatsDto,
    description: '耗时挑战统计数据',
  })
  @ApiResponse({
    status: 200,
    description: '耗时挑战数据上报成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '耗时挑战数据上报成功' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 12345 },
            username: { type: 'string', example: 'player001' },
            avatar: { type: 'string', example: 'https://example.com/avatar.jpg' },
            timeMs: { type: 'number', example: 15500 },
            displayTime: { type: 'string', example: '15.500s' },
            isNewRecord: { type: 'boolean', example: true },
            updatedAt: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
          },
        },
      },
    },
  })
  async reportTimeChallengeStats(@Body() reportDto: ReportTimeChallengeStatsDto) {
    this.logger.log(`接收到耗时挑战统计数据上报请求: ${JSON.stringify(reportDto)}`);

    try {
      // 先获取现有记录以判断是否为新记录
      const existingStats = await this.statsService.findPlayerStatsByType(
        reportDto.id,
        'time_challenge' as any
      );
      
      const playerStats = await this.statsService.reportTimeChallengeStats(reportDto);
      
      // 判断是否为新记录
      const isNewRecord = !existingStats || reportDto.timeMs < existingStats.statValue;

      // 格式化显示时间
      const seconds = Math.floor(reportDto.timeMs / 1000);
      const milliseconds = reportDto.timeMs % 1000;
      const displayTime = `${seconds}.${milliseconds.toString().padStart(3, '0')}s`;

      return {
        success: true,
        message: isNewRecord ? '耗时挑战数据上报成功，创造新记录！' : '耗时挑战数据上报成功',
        data: {
          id: playerStats.playerId,
          username: playerStats.username,
          avatar: playerStats.avatar,
          timeMs: playerStats.statValue,
          displayTime,
          isNewRecord,
          updatedAt: playerStats.updatedAt,
        },
      };
    } catch (error) {
      this.logger.error(`耗时挑战统计数据上报失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
