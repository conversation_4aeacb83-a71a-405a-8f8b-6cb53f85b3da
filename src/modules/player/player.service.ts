import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Player } from '../../entities/player.entity';
import { ReportPlayerDto } from './dto/report-player.dto';

/**
 * 玩家服务
 * 处理玩家数据的业务逻辑
 */
@Injectable()
export class PlayerService {
  private readonly logger = new Logger(PlayerService.name);

  constructor(
    @InjectRepository(Player)
    private readonly playerRepository: Repository<Player>,
  ) {}

  /**
   * 上报玩家数据
   * 使用 UPSERT 操作，如果玩家存在则更新，不存在则创建
   */
  async reportPlayerData(reportDto: ReportPlayerDto): Promise<Player> {
    this.logger.log(`上报玩家数据: ID=${reportDto.id}, 用户名=${reportDto.username}, 金币=${reportDto.coins}`);

    try {
      // 使用 save 方法实现 UPSERT 操作
      const player = this.playerRepository.create({
        id: reportDto.id,
        username: reportDto.username,
        avatar: reportDto.avatar,
        coins: reportDto.coins,
      });

      const savedPlayer = await this.playerRepository.save(player);
      
      this.logger.log(`玩家数据上报成功: ID=${savedPlayer.id}`);
      return savedPlayer;
    } catch (error) {
      this.logger.error(`玩家数据上报失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据ID查找玩家
   */
  async findPlayerById(id: number): Promise<Player | null> {
    return this.playerRepository.findOne({ where: { id } });
  }

  /**
   * 获取玩家总数
   */
  async getPlayerCount(): Promise<number> {
    return this.playerRepository.count();
  }

  /**
   * 获取按金币排序的玩家列表
   * 用于生成排行榜
   */
  async getTopPlayersByCoins(limit: number = 100): Promise<Player[]> {
    return this.playerRepository.find({
      order: { coins: 'DESC', updatedAt: 'DESC' },
      take: limit,
    });
  }
}
