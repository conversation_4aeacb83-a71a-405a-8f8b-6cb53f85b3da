import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Player } from '../../entities/player.entity';
import { PlayerController } from './player.controller';
import { PlayerService } from './player.service';

/**
 * 玩家模块
 * 封装玩家相关的控制器、服务和实体
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([Player]),
  ],
  controllers: [PlayerController],
  providers: [PlayerService],
  exports: [PlayerService], // 导出服务供其他模块使用
})
export class PlayerModule {}
