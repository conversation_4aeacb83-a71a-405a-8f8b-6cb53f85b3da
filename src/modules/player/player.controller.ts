import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
} from '@nestjs/swagger';
import { PlayerService } from './player.service';
import { ReportPlayerDto } from './dto/report-player.dto';

/**
 * 玩家控制器
 * 处理玩家相关的HTTP请求
 */
@ApiTags('玩家管理')
@Controller('api/players')
export class PlayerController {
  private readonly logger = new Logger(PlayerController.name);

  constructor(private readonly playerService: PlayerService) {}

  /**
   * 上报玩家数据接口
   */
  @Post('report')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '上报玩家数据',
    description: '客户端调用此接口上报玩家的最新统计数据，服务端接收后更新或插入到数据库中',
  })
  @ApiBody({
    type: ReportPlayerDto,
    description: '玩家数据',
  })
  @ApiResponse({
    status: 200,
    description: '数据上报成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '数据上报成功' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 12345 },
            username: { type: 'string', example: 'player001' },
            avatar: { type: 'string', example: 'https://example.com/avatar.jpg' },
            coins: { type: 'number', example: 10000 },
            updatedAt: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: '请求参数错误' },
        errors: {
          type: 'array',
          items: { type: 'string' },
          example: ['玩家ID必须是数字', '用户名不能为空'],
        },
      },
    },
  })
  async reportPlayerData(@Body() reportDto: ReportPlayerDto) {
    this.logger.log(`接收到玩家数据上报请求: ${JSON.stringify(reportDto)}`);

    try {
      const player = await this.playerService.reportPlayerData(reportDto);

      return {
        success: true,
        message: '数据上报成功',
        data: {
          id: player.id,
          username: player.username,
          avatar: player.avatar,
          coins: player.coins,
          updatedAt: player.updatedAt,
        },
      };
    } catch (error) {
      this.logger.error(`玩家数据上报失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
