import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';

/**
 * 应用程序启动入口
 */
async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  // 创建 NestJS 应用实例
  const app = await NestFactory.create(AppModule);

  // 获取配置服务
  const configService = app.get(ConfigService);
  const port = configService.get<number>('PORT', 3000);
  const nodeEnv = configService.get<string>('NODE_ENV', 'development');

  // 启用全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true, // 自动转换类型
      whitelist: true, // 过滤掉不在 DTO 中定义的属性
      forbidNonWhitelisted: true, // 如果有未定义的属性则抛出错误
      transformOptions: {
        enableImplicitConversion: true, // 启用隐式类型转换
      },
    }),
  );

  // 启用 CORS（跨域资源共享）
  app.enableCors({
    origin: true, // 开发环境允许所有来源，生产环境应该配置具体域名
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  });

  // 配置 Swagger API 文档（仅在开发环境）
  if (nodeEnv === 'development') {
    const config = new DocumentBuilder()
      .setTitle('排行榜服务器 API')
      .setDescription('简洁易维护的排行榜服务器 API 文档')
      .setVersion('1.0')
      .addTag('玩家管理', '玩家数据上报相关接口')
      .addTag('排行榜', '排行榜查询和管理相关接口')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        displayRequestDuration: true,
      },
    });

    logger.log(`Swagger API 文档已启用: http://localhost:${port}/api/docs`);
  }

  // 启动应用
  await app.listen(port);
  
  logger.log(`🚀 排行榜服务器启动成功！`);
  logger.log(`📍 服务地址: http://localhost:${port}`);
  logger.log(`🌍 运行环境: ${nodeEnv}`);
  
  if (nodeEnv === 'development') {
    logger.log(`📚 API文档: http://localhost:${port}/api/docs`);
  }
}

// 启动应用并处理未捕获的异常
bootstrap().catch((error) => {
  const logger = new Logger('Bootstrap');
  logger.error('应用启动失败:', error);
  process.exit(1);
});
