import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

/**
 * 排行榜实体
 * 存储不同类型的排行榜数据
 */
@Entity('leaderboards')
@Index('idx_type_rank', ['leaderboardType', 'rankPosition'])
@Index('idx_type_generated', ['leaderboardType', 'generatedAt'])
export class Leaderboard {
  /**
   * 自增主键
   */
  @PrimaryGeneratedColumn({
    type: 'int',
    comment: '自增主键'
  })
  id: number;

  /**
   * 排行榜类型
   * coins: 金币排行榜
   * time_challenge: 耗时挑战排行榜
   */
  @Column({ 
    type: 'varchar', 
    length: 50, 
    nullable: false,
    comment: '排行榜类型'
  })
  leaderboardType: string;

  /**
   * 排名位置
   */
  @Column({ 
    type: 'int', 
    nullable: false,
    comment: '排名位置'
  })
  rankPosition: number;

  /**
   * 玩家ID
   */
  @Column({ 
    type: 'bigint', 
    nullable: false,
    comment: '玩家ID'
  })
  playerId: number;

  /**
   * 用户名
   */
  @Column({ 
    type: 'varchar', 
    length: 50, 
    nullable: false,
    comment: '用户名'
  })
  username: string;

  /**
   * 头像URL
   */
  @Column({ 
    type: 'varchar', 
    length: 255, 
    nullable: true,
    comment: '头像URL'
  })
  avatar: string;

  /**
   * 统计数值
   */
  @Column({ 
    type: 'bigint', 
    nullable: false,
    comment: '统计数值'
  })
  statValue: number;

  /**
   * 显示文本
   * 用于前端显示的格式化文本
   */
  @Column({ 
    type: 'varchar', 
    length: 100, 
    nullable: true,
    comment: '显示文本'
  })
  displayText: string;

  /**
   * 生成时间
   */
  @CreateDateColumn({
    type: 'timestamp',
    comment: '生成时间'
  })
  generatedAt: Date;
}
