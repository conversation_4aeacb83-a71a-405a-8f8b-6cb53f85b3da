import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

/**
 * 排行榜缓存实体
 * 存储排序后的排行榜数据
 */
@Entity('leaderboard_cache')
@Index('idx_rank', ['rankPosition'])
@Index('idx_generated_at', ['generatedAt'])
export class LeaderboardCache {
  /**
   * 自增主键
   */
  @PrimaryGeneratedColumn({
    type: 'int',
    comment: '自增主键'
  })
  id: number;

  /**
   * 排名位置
   */
  @Column({ 
    type: 'int', 
    nullable: false,
    comment: '排名位置'
  })
  rankPosition: number;

  /**
   * 玩家ID
   */
  @Column({ 
    type: 'bigint', 
    nullable: false,
    comment: '玩家ID'
  })
  playerId: number;

  /**
   * 用户名
   */
  @Column({ 
    type: 'varchar', 
    length: 50, 
    nullable: false,
    comment: '用户名'
  })
  username: string;

  /**
   * 头像URL
   */
  @Column({ 
    type: 'varchar', 
    length: 255, 
    nullable: true,
    comment: '头像URL'
  })
  avatar: string;

  /**
   * 金币数量
   */
  @Column({ 
    type: 'bigint', 
    nullable: false,
    comment: '金币数量'
  })
  coins: number;

  /**
   * 生成时间
   */
  @CreateDateColumn({
    type: 'timestamp',
    comment: '生成时间'
  })
  generatedAt: Date;
}
