import {
  Entity,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * 玩家实体
 * 存储玩家的基础数据和金币信息
 */
@Entity('players')
@Index('idx_coins', ['coins'])
@Index('idx_updated_at', ['updatedAt'])
export class Player {
  /**
   * 玩家ID (主键)
   */
  @PrimaryColumn({ type: 'bigint', comment: '玩家ID' })
  id: number;

  /**
   * 用户名
   */
  @Column({ 
    type: 'varchar', 
    length: 50, 
    nullable: false,
    comment: '用户名'
  })
  username: string;

  /**
   * 头像URL
   */
  @Column({ 
    type: 'varchar', 
    length: 255, 
    nullable: true,
    comment: '头像URL'
  })
  avatar: string;

  /**
   * 金币数量
   */
  @Column({ 
    type: 'bigint', 
    default: 0,
    comment: '金币数量'
  })
  coins: number;

  /**
   * 创建时间
   */
  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间'
  })
  createdAt: Date;

  /**
   * 更新时间
   */
  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间'
  })
  updatedAt: Date;
}
