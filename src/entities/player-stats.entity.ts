import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  Unique,
} from 'typeorm';

/**
 * 玩家统计数据实体
 * 存储不同类型的玩家统计数据
 */
@Entity('player_stats')
@Index('idx_stat_type_value', ['statType', 'statValue'])
@Index('idx_stat_type_player', ['statType', 'playerId'])
@Index('idx_updated_at', ['updatedAt'])
@Unique('unique_player_stat_type', ['playerId', 'statType'])
export class PlayerStats {
  /**
   * 自增主键
   */
  @PrimaryGeneratedColumn({
    type: 'int',
    comment: '自增主键'
  })
  id: number;

  /**
   * 玩家ID
   */
  @Column({ 
    type: 'bigint', 
    nullable: false,
    comment: '玩家ID'
  })
  playerId: number;

  /**
   * 用户名
   */
  @Column({ 
    type: 'varchar', 
    length: 50, 
    nullable: false,
    comment: '用户名'
  })
  username: string;

  /**
   * 头像URL
   */
  @Column({ 
    type: 'varchar', 
    length: 255, 
    nullable: true,
    comment: '头像URL'
  })
  avatar: string;

  /**
   * 统计类型
   * coins: 金币数量
   * time_challenge: 固定金币挑战耗时
   */
  @Column({ 
    type: 'varchar', 
    length: 50, 
    nullable: false,
    comment: '统计类型'
  })
  statType: string;

  /**
   * 统计数值
   * 对于金币类型：存储金币数量
   * 对于耗时类型：存储耗时（毫秒）
   */
  @Column({ 
    type: 'bigint', 
    nullable: false,
    comment: '统计数值'
  })
  statValue: number;

  /**
   * 额外数据（JSON格式）
   * 可存储特定统计类型的额外信息
   */
  @Column({ 
    type: 'json', 
    nullable: true,
    comment: '额外数据'
  })
  extraData: any;

  /**
   * 创建时间
   */
  @CreateDateColumn({
    type: 'timestamp',
    comment: '创建时间'
  })
  createdAt: Date;

  /**
   * 更新时间
   */
  @UpdateDateColumn({
    type: 'timestamp',
    comment: '更新时间'
  })
  updatedAt: Date;
}
