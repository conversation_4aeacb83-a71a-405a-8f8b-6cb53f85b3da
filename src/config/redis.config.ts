import { ConfigService } from '@nestjs/config';
import KeyvRedis from '@keyv/redis';
import { Keyv } from 'keyv';

/**
 * Redis 配置工厂函数
 * 根据环境变量生成 Redis 缓存配置
 * 使用 ioredis 5.3.0 和 Keyv 适配器
 */
export const getRedisConfig = (configService: ConfigService) => {
  const host = configService.get<string>('REDIS_HOST', 'localhost');
  const port = configService.get<number>('REDIS_PORT', 6379);
  const password = configService.get<string>('REDIS_PASSWORD');
  const db = configService.get<number>('REDIS_DB', 0);

  // 构建 Redis 连接 URL
  let redisUrl = `redis://${host}:${port}/${db}`;
  if (password) {
    redisUrl = `redis://:${password}@${host}:${port}/${db}`;
  }

  // 直接使用 URL 创建 Keyv Redis 适配器
  const keyvRedis = new KeyvRedis(redisUrl);

  return {
    stores: [
      new Keyv({
        store: keyvRedis,
        namespace: 'rankserver', // 添加命名空间，便于识别
      }),
    ],
    ttl: configService.get<number>('LEADERBOARD_CACHE_TTL', 86400) * 1000, // 转换为毫秒
  };
};
