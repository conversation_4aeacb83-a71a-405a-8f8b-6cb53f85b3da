import { ConfigService } from '@nestjs/config';
import KeyvRedis from '@keyv/redis';

/**
 * Redis 配置工厂函数
 * 根据环境变量生成 Redis 缓存配置
 */
export const getRedisConfig = (configService: ConfigService) => {
  const host = configService.get<string>('REDIS_HOST', 'localhost');
  const port = configService.get<number>('REDIS_PORT', 6379);
  const password = configService.get<string>('REDIS_PASSWORD');
  
  // 构建 Redis 连接 URL
  let redisUrl = `redis://${host}:${port}`;
  if (password) {
    redisUrl = `redis://:${password}@${host}:${port}`;
  }

  return {
    stores: [
      createKeyv(redisUrl, {
        // 默认 TTL 24小时
        ttl: configService.get<number>('LEADERBOARD_CACHE_TTL', 86400) * 1000,
      }),
    ],
  };
};
