import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { PlayerStats } from '../entities/player-stats.entity';
import { Leaderboard } from '../entities/leaderboard.entity';

/**
 * 数据库配置工厂函数
 * 根据环境变量生成 TypeORM 配置
 */
export const getDatabaseConfig = (configService: ConfigService): TypeOrmModuleOptions => {
  const isProduction = configService.get<string>('NODE_ENV') === 'production';

  return {
    type: 'mysql',
    host: configService.get<string>('DB_HOST', 'localhost'),
    port: configService.get<number>('DB_PORT', 3306),
    username: configService.get<string>('DB_USERNAME', 'root'),
    password: configService.get<string>('DB_PASSWORD', 'password'),
    database: configService.get<string>('DB_DATABASE', 'rank_server'),
    entities: [PlayerStats, Leaderboard],

    // 同步配置：只在开发环境启用
    synchronize: !isProduction,

    // 日志配置：生产环境关闭所有日志，开发环境只显示错误
    logging: isProduction ? false : ['error'],
    logger: 'simple-console',

    // 数据库配置
    timezone: '+08:00',
    charset: 'utf8mb4',
    // MySQL2 连接池配置
    extra: {
      connectionLimit: 10,        // 最大连接数
      queueLimit: 0,             // 队列限制（0表示无限制）
      // 字符集配置
      charset: 'utf8mb4',
    },
  };
};
