# API 接口文档

## 概述

排行榜服务器提供 RESTful API 接口，支持玩家数据上报和排行榜查询功能。

## 基础信息

- **Base URL**: `http://localhost:3000`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 接口列表

### 1. 玩家数据上报

**接口地址**: `POST /api/players/report`

**功能描述**: 客户端调用此接口上报玩家的最新统计数据，服务端接收后更新或插入到数据库中。

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| id | number | 是 | 玩家ID | 12345 |
| username | string | 是 | 用户名，最大50字符 | "player001" |
| avatar | string | 否 | 头像URL，最大255字符 | "https://example.com/avatar.jpg" |
| coins | number | 是 | 金币数量，非负整数 | 10000 |

**请求示例**:
```json
{
  "id": 12345,
  "username": "player001",
  "avatar": "https://example.com/avatar.jpg",
  "coins": 10000
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "数据上报成功",
  "data": {
    "id": 12345,
    "username": "player001",
    "avatar": "https://example.com/avatar.jpg",
    "coins": 10000,
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "请求参数错误",
  "errors": [
    "玩家ID必须是数字",
    "用户名不能为空"
  ]
}
```

### 2. 获取排行榜

**接口地址**: `GET /api/leaderboard`

**功能描述**: 获取当前的排行榜数据，优先从缓存获取以提高性能。

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 默认值 |
|--------|------|------|------|--------|
| limit | number | 否 | 返回的排行榜条数，最大100 | 100 |

**请求示例**:
```
GET /api/leaderboard?limit=50
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "leaderboard": [
      {
        "rank": 1,
        "id": 12345,
        "username": "player001",
        "avatar": "https://example.com/avatar.jpg",
        "coins": 50000
      },
      {
        "rank": 2,
        "id": 12346,
        "username": "player002",
        "avatar": "https://example.com/avatar2.jpg",
        "coins": 45000
      }
    ],
    "generatedAt": "2024-01-01T00:00:00.000Z",
    "totalPlayers": 1000,
    "fromCache": true
  }
}
```

### 3. 手动刷新排行榜

**接口地址**: `POST /api/leaderboard/refresh`

**功能描述**: 手动触发排行榜数据的重新生成和缓存更新。

**请求参数**: 无

**响应示例**:
```json
{
  "success": true,
  "message": "排行榜刷新任务已启动",
  "taskId": "refresh_20240101_120000"
}
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

## 错误处理

所有接口在发生错误时都会返回统一的错误格式：

```json
{
  "success": false,
  "message": "错误描述",
  "errors": ["具体错误信息数组"]
}
```

## 数据验证规则

### 玩家数据验证

- **id**: 必须是正整数
- **username**: 必须是字符串，长度1-50字符
- **avatar**: 可选，必须是有效的URL格式
- **coins**: 必须是非负整数，最大值999999999999

### 排行榜查询验证

- **limit**: 可选，必须是1-100之间的整数

## 性能说明

- **排行榜查询**: 优先从Redis缓存获取，缓存TTL为24小时
- **数据上报**: 使用UPSERT操作，支持高并发
- **排行榜生成**: 异步执行，不阻塞API响应

## 示例代码

### JavaScript/Node.js

```javascript
// 上报玩家数据
const reportData = {
  id: 12345,
  username: "player001",
  avatar: "https://example.com/avatar.jpg",
  coins: 10000
};

const response = await fetch('http://localhost:3000/api/players/report', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(reportData),
});

const result = await response.json();
console.log(result);

// 获取排行榜
const leaderboard = await fetch('http://localhost:3000/api/leaderboard?limit=10');
const leaderboardData = await leaderboard.json();
console.log(leaderboardData);
```

### Python

```python
import requests
import json

# 上报玩家数据
report_data = {
    "id": 12345,
    "username": "player001",
    "avatar": "https://example.com/avatar.jpg",
    "coins": 10000
}

response = requests.post(
    'http://localhost:3000/api/players/report',
    headers={'Content-Type': 'application/json'},
    data=json.dumps(report_data)
)

print(response.json())

# 获取排行榜
leaderboard = requests.get('http://localhost:3000/api/leaderboard?limit=10')
print(leaderboard.json())
```

## 注意事项

1. 所有时间字段均为 ISO 8601 格式的 UTC 时间
2. 排行榜按金币数量降序排列，金币相同时按更新时间降序排列
3. 建议客户端实现适当的重试机制和错误处理
4. 生产环境建议配置适当的请求频率限制
