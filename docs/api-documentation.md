# 排行榜服务器 API 文档

## 概述

本文档描述了排行榜服务器的所有API接口，包括请求格式、响应格式、安全要求和使用示例。

## 基础信息

- **服务地址**：`http://localhost:3000`
- **API版本**：v1.0
- **内容类型**：`application/json`
- **字符编码**：UTF-8

## 安全认证

### 签名验证

所有数据上报接口都需要签名验证，查询接口需要频率限制验证。

#### 必需的请求头

| 头名称 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `x-signature` | string | HMAC-SHA256签名 | `a1b2c3d4e5f6...` |
| `x-timestamp` | string | 请求时间戳（毫秒） | `1640995200000` |
| `x-nonce` | string | 随机数（防重放） | `abc123def456` |

#### 签名算法

```javascript
const signString = `${timestamp}${nonce}${JSON.stringify(sortedData)}${secretKey}`;
const signature = crypto.createHash('sha256').update(signString).digest('hex');
```

## 统计数据接口

### 上报金币统计数据

上报玩家的金币统计数据到服务器。

**接口地址**：`POST /api/stats/coins/report`

**安全要求**：需要签名验证

**频率限制**：10次/分钟

#### 请求参数

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| id | number | 是 | 玩家ID | 12345 |
| username | string | 是 | 用户名 | "player001" |
| avatar | string | 否 | 头像URL | "https://example.com/avatar.jpg" |
| coins | number | 是 | 金币数量 | 10000 |

#### 请求示例

```bash
curl -X POST http://localhost:3000/api/stats/coins/report \
  -H "Content-Type: application/json" \
  -H "x-signature: a1b2c3d4e5f6..." \
  -H "x-timestamp: 1640995200000" \
  -H "x-nonce: abc123def456" \
  -d '{
    "id": 12345,
    "username": "player001",
    "avatar": "https://example.com/avatar.jpg",
    "coins": 10000
  }'
```

#### 响应格式

**成功响应 (200)**：
```json
{
  "success": true,
  "message": "金币数据上报成功",
  "data": {
    "id": 12345,
    "username": "player001",
    "avatar": "https://example.com/avatar.jpg",
    "coins": 10000,
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**错误响应**：
- `401 Unauthorized`：签名验证失败
- `400 Bad Request`：数据验证失败
- `429 Too Many Requests`：频率限制

### 上报耗时挑战统计数据

上报玩家完成固定金币挑战的耗时数据。

**接口地址**：`POST /api/stats/time-challenge/report`

**安全要求**：需要签名验证

**频率限制**：5次/分钟

#### 请求参数

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| id | number | 是 | 玩家ID | 12345 |
| username | string | 是 | 用户名 | "player001" |
| avatar | string | 否 | 头像URL | "https://example.com/avatar.jpg" |
| timeMs | number | 是 | 耗时（毫秒） | 15500 |
| targetCoins | number | 否 | 目标金币数量 | 1000 |

#### 请求示例

```bash
curl -X POST http://localhost:3000/api/stats/time-challenge/report \
  -H "Content-Type: application/json" \
  -H "x-signature: a1b2c3d4e5f6..." \
  -H "x-timestamp: 1640995200000" \
  -H "x-nonce: abc123def456" \
  -d '{
    "id": 12345,
    "username": "player001",
    "avatar": "https://example.com/avatar.jpg",
    "timeMs": 15500,
    "targetCoins": 1000
  }'
```

#### 响应格式

**成功响应 (200)**：
```json
{
  "success": true,
  "message": "耗时挑战数据上报成功，创造新记录！",
  "data": {
    "id": 12345,
    "username": "player001",
    "avatar": "https://example.com/avatar.jpg",
    "timeMs": 15500,
    "displayTime": "15.500s",
    "isNewRecord": true,
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

## 排行榜接口

### 获取排行榜类型

获取所有可用的排行榜类型和配置信息。

**接口地址**：`GET /api/leaderboard/types`

**安全要求**：频率限制

**频率限制**：30次/分钟

#### 响应格式

```json
{
  "success": true,
  "data": {
    "general": [
      {
        "type": "coins",
        "name": "金币排行榜",
        "description": "按玩家金币数量排序",
        "unit": "金币"
      }
    ],
    "challenges": [
      {
        "type": "time_challenge_1000",
        "name": "1K金币挑战",
        "description": "最快达到1000金币挑战",
        "targetCoins": 1000
      },
      {
        "type": "time_challenge_10000",
        "name": "1万金币挑战",
        "description": "最快达到1万金币挑战",
        "targetCoins": 10000
      }
    ]
  }
}
```

### 获取指定类型排行榜

获取指定类型的排行榜数据。

**接口地址**：`GET /api/leaderboard/{type}`

**安全要求**：频率限制

**频率限制**：30次/分钟

#### 路径参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| type | string | 排行榜类型 | coins, time_challenge_1000 |

#### 查询参数

| 参数名 | 类型 | 必需 | 说明 | 默认值 |
|--------|------|------|------|-------|
| limit | number | 否 | 返回条数 | 100 |

#### 请求示例

```bash
curl -X GET "http://localhost:3000/api/leaderboard/coins?limit=10"
```

#### 响应格式

```json
{
  "success": true,
  "data": {
    "type": "coins",
    "leaderboard": [
      {
        "rank": 1,
        "id": 12345,
        "username": "player001",
        "avatar": "https://example.com/avatar.jpg",
        "value": 50000,
        "displayText": "50,000 金币"
      }
    ],
    "generatedAt": "2024-01-01T00:00:00.000Z",
    "totalPlayers": 1000,
    "fromCache": true
  }
}
```

### 获取挑战目标

获取所有可用的挑战目标配置。

**接口地址**：`GET /api/leaderboard/challenges`

**安全要求**：频率限制

**频率限制**：30次/分钟

#### 响应格式

```json
{
  "success": true,
  "data": [
    {
      "coins": 1000,
      "name": "challenge_1k",
      "displayName": "1K金币挑战",
      "description": "最快达到1000金币挑战",
      "leaderboardType": "time_challenge_1000"
    }
  ]
}
```

## 错误码说明

| 状态码 | 说明 | 常见原因 |
|--------|------|----------|
| 200 | 成功 | 请求处理成功 |
| 400 | 请求错误 | 参数格式错误、数据验证失败 |
| 401 | 未授权 | 签名验证失败、缺少安全头 |
| 429 | 请求过多 | 触发频率限制 |
| 500 | 服务器错误 | 内部服务器错误 |

## 错误响应格式

```json
{
  "message": "错误描述",
  "error": "错误类型",
  "statusCode": 400,
  "errors": ["详细错误信息1", "详细错误信息2"]
}
```

## 频率限制

### 限制策略

| 接口类型 | 时间窗口 | 最大请求数 |
|----------|----------|------------|
| 金币上报 | 1分钟 | 10次 |
| 耗时挑战上报 | 1分钟 | 5次 |
| 排行榜查询 | 1分钟 | 30次 |

### 响应头

当触发频率限制时，响应会包含以下头信息：

| 头名称 | 说明 |
|--------|------|
| `X-RateLimit-Remaining` | 剩余请求次数 |
| `X-RateLimit-Reset` | 限制重置时间戳 |

## 客户端SDK

### JavaScript SDK

```javascript
const SecureApiClient = require('./secure-api-client');

const client = new SecureApiClient('your-secret-key');

// 上报金币
await client.reportCoins(playerId, username, avatar, coins);

// 上报耗时挑战
await client.reportTimeChallenge(playerId, username, avatar, timeMs, targetCoins);

// 获取排行榜
const leaderboard = await client.getLeaderboard('coins', 10);
```

## 测试工具

### 签名生成工具

```bash
node scripts/generate-signature.js \
  --data '{"id":1001,"coins":5000}' \
  --timestamp $(date +%s)000 \
  --nonce $(openssl rand -hex 16)
```

### API测试脚本

```bash
# 测试安全API
node examples/client-signature-example.js

# 测试挑战排行榜
node test-challenge-leaderboards.js
```

---

更多详细信息请参考 [安全防护指南](./security-guide.md)。
