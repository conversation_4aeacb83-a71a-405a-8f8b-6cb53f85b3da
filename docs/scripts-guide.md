# 服务端脚本使用指南

## 概述

本文档介绍了排行榜服务器提供的各种服务端脚本的使用方法和功能。

## 排行榜刷新脚本

### 完整版刷新脚本

**文件位置**: `scripts/refresh-leaderboards.js`

**功能**: 提供完整的排行榜刷新功能，支持刷新指定类型或所有类型的排行榜。

#### 基本用法

```bash
# 刷新所有排行榜
node scripts/refresh-leaderboards.js

# 刷新指定类型排行榜
node scripts/refresh-leaderboards.js --type coins

# 显示详细输出
node scripts/refresh-leaderboards.js --verbose

# 显示帮助信息
node scripts/refresh-leaderboards.js --help
```

#### 命令行选项

| 选项 | 简写 | 说明 | 示例 |
|------|------|------|------|
| `--type` | `-t` | 指定排行榜类型 | `--type coins` |
| `--verbose` | `-v` | 显示详细输出 | `--verbose` |
| `--help` | `-h` | 显示帮助信息 | `--help` |

#### 支持的排行榜类型

- `coins` - 金币排行榜
- `time_challenge_1000` - 1K金币挑战排行榜
- `time_challenge_10000` - 1万金币挑战排行榜
- `time_challenge_100000` - 10万金币挑战排行榜
- `time_challenge_1000000` - 100万金币挑战排行榜
- `time_challenge_100000000` - 1亿金币挑战排行榜

#### 使用示例

```bash
# 刷新金币排行榜
node scripts/refresh-leaderboards.js --type coins

# 刷新1K金币挑战排行榜
node scripts/refresh-leaderboards.js --type time_challenge_1000

# 刷新所有排行榜并显示详细信息
node scripts/refresh-leaderboards.js --verbose
```

#### 输出示例

```
🚀 排行榜刷新脚本启动...

🔄 开始刷新所有排行榜...
📊 发现 6 种排行榜类型:
......

📊 刷新完成统计:
  ✅ 成功: 6 个
  ❌ 失败: 0 个
  ⏱️  总耗时: 2.3s

🎉 所有排行榜刷新成功!
```

### 快速刷新脚本

**文件位置**: `scripts/quick-refresh.js`

**功能**: 简化版排行榜刷新脚本，快速刷新所有排行榜。

#### 基本用法

```bash
node scripts/quick-refresh.js
```

#### 输出示例

```
🚀 快速刷新所有排行榜...
✅ 所有排行榜刷新完成! 耗时: 2s
```

## 数据库管理脚本

### 数据库初始化脚本

**文件位置**: `scripts/create-database.js`

**功能**: 创建数据库和初始化表结构，插入测试数据。

#### 基本用法

```bash
node scripts/create-database.js
```

## 测试脚本

### 安全API测试

**文件位置**: `examples/client-signature-example.js`

**功能**: 演示如何使用签名验证进行安全的API调用。

```bash
node examples/client-signature-example.js
```

### 挑战排行榜测试

**文件位置**: `test-challenge-leaderboards.js`

**功能**: 测试挑战排行榜的完整功能。

```bash
node test-challenge-leaderboards.js
```

### 安全攻击测试

**文件位置**: `test-security-attacks.js`

**功能**: 测试系统的安全防护能力。

```bash
node test-security-attacks.js
```

## 定时任务

### 自动排行榜刷新

系统会自动在每日凌晨0点刷新所有排行榜。如果需要修改定时任务，可以在环境变量中配置：

```bash
# 每日凌晨0点（默认）
LEADERBOARD_UPDATE_CRON=0 0 0 * * *

# 每小时刷新
LEADERBOARD_UPDATE_CRON=0 0 * * * *

# 每30分钟刷新
LEADERBOARD_UPDATE_CRON=0 */30 * * * *
```

## 运维建议

### 日常维护

1. **定期检查排行榜数据**
   ```bash
   # 检查所有排行榜状态
   node scripts/refresh-leaderboards.js --verbose
   ```

2. **监控脚本执行**
   - 建议将脚本输出重定向到日志文件
   - 设置监控告警，当脚本执行失败时及时通知

3. **性能优化**
   - 在低峰期执行大批量刷新
   - 监控脚本执行时间，优化慢查询

### 故障处理

1. **排行榜数据异常**
   ```bash
   # 强制刷新所有排行榜
   node scripts/refresh-leaderboards.js --verbose
   ```

2. **特定类型排行榜问题**
   ```bash
   # 单独刷新问题排行榜
   node scripts/refresh-leaderboards.js --type coins --verbose
   ```

3. **数据库连接问题**
   - 检查数据库连接配置
   - 确认Redis服务状态
   - 查看应用日志

### 自动化部署

#### 使用cron定时执行

```bash
# 编辑crontab
crontab -e

# 添加定时任务（每日凌晨2点刷新排行榜）
0 2 * * * cd /path/to/rank-server && node scripts/quick-refresh.js >> /var/log/leaderboard-refresh.log 2>&1
```

#### 使用systemd服务

创建服务文件 `/etc/systemd/system/leaderboard-refresh.service`:

```ini
[Unit]
Description=Leaderboard Refresh Service
After=network.target

[Service]
Type=oneshot
User=rankserver
WorkingDirectory=/path/to/rank-server
ExecStart=/usr/bin/node scripts/quick-refresh.js
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

创建定时器文件 `/etc/systemd/system/leaderboard-refresh.timer`:

```ini
[Unit]
Description=Run leaderboard refresh daily
Requires=leaderboard-refresh.service

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target
```

启用服务：

```bash
sudo systemctl enable leaderboard-refresh.timer
sudo systemctl start leaderboard-refresh.timer
```

## 脚本开发指南

### 创建新脚本

1. **基本结构**
   ```javascript
   #!/usr/bin/env node
   
   const { NestFactory } = require('@nestjs/core');
   const { AppModule } = require('../dist/app.module');
   
   async function main() {
     const app = await NestFactory.createApplicationContext(AppModule);
     
     try {
       // 脚本逻辑
     } finally {
       await app.close();
     }
   }
   
   if (require.main === module) {
     main();
   }
   ```

2. **错误处理**
   - 使用try-catch包装主要逻辑
   - 设置适当的退出码
   - 提供有意义的错误信息

3. **日志输出**
   - 使用统一的日志格式
   - 区分不同级别的输出
   - 支持静默模式

### 最佳实践

1. **性能考虑**
   - 避免在脚本中进行大量同步操作
   - 合理使用批处理
   - 监控内存使用

2. **安全考虑**
   - 验证输入参数
   - 避免SQL注入
   - 限制脚本执行权限

3. **可维护性**
   - 添加详细的注释
   - 提供帮助信息
   - 支持配置文件

---

更多信息请参考 [API文档](./api-documentation.md) 和 [安全防护指南](./security-guide.md)。
