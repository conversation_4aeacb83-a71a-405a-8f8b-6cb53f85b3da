# 排行榜服务器安全防护指南

## 概述

本文档详细介绍了排行榜服务器的安全防护架构、实现原理和使用方法。系统采用多层安全防护策略，有效防止数据伪造、重放攻击、频率滥用等安全威胁。

## 安全威胁分析

### 主要安全风险

1. **数据伪造攻击**
   - 风险：客户端可任意修改金币数量、耗时等关键数据
   - 影响：排行榜数据失真，游戏公平性受损
   - 防护：数据签名验证

2. **身份伪造攻击**
   - 风险：恶意用户冒充其他玩家ID进行数据上报
   - 影响：他人账户数据被篡改
   - 防护：签名验证 + 玩家令牌

3. **重放攻击**
   - 风险：攻击者截获并重复发送相同的有效请求
   - 影响：重复执行操作，数据异常
   - 防护：nonce机制 + 时间戳验证

4. **频率滥用攻击**
   - 风险：恶意客户端无限制频繁请求
   - 影响：服务器资源耗尽，正常用户无法访问
   - 防护：频率限制

5. **异常数据攻击**
   - 风险：提交不合理的游戏数据（如负数金币、超快耗时）
   - 影响：排行榜数据污染，影响其他玩家体验
   - 防护：数据合理性验证

## 安全架构设计

### 多层防护架构

```
┌─────────────────┐
│   客户端请求     │
└─────────┬───────┘
          │
┌─────────▼───────┐
│  签名验证层      │ ← 验证请求签名、时间戳
└─────────┬───────┘
          │
┌─────────▼───────┐
│  防重放层       │ ← 检查nonce，防止重放
└─────────┬───────┘
          │
┌─────────▼───────┐
│  频率限制层     │ ← 限制请求频率
└─────────┬───────┘
          │
┌─────────▼───────┐
│  数据验证层     │ ← 验证数据合理性
└─────────┬───────┘
          │
┌─────────▼───────┐
│  业务逻辑层     │ ← 处理业务逻辑
└─────────────────┘
```

### 核心组件

1. **CryptoService** - 加密服务
   - 签名生成与验证
   - 时间戳验证
   - 玩家令牌管理

2. **AntiReplayService** - 防重放服务
   - nonce缓存管理
   - 重放攻击检测

3. **RateLimitService** - 频率限制服务
   - 滑动窗口算法
   - 多级限制策略

4. **DataValidatorService** - 数据验证服务
   - 数据合理性检查
   - 异常数据检测

5. **SecurityGuard** - 安全守卫
   - 统一安全检查入口
   - 多层验证协调

## 签名验证机制

### 签名算法

使用 HMAC-SHA256 算法生成签名：

```
signature = HMAC-SHA256(timestamp + nonce + sortedData + secretKey)
```

### 签名流程

1. **客户端生成签名**：
   ```javascript
   const timestamp = Date.now();
   const nonce = generateRandomNonce();
   const sortedData = sortObjectKeys(requestData);
   const signString = `${timestamp}${nonce}${JSON.stringify(sortedData)}${secretKey}`;
   const signature = crypto.createHash('sha256').update(signString).digest('hex');
   ```

2. **服务端验证签名**：
   - 提取请求头中的签名、时间戳、nonce
   - 使用相同算法重新计算签名
   - 比较计算结果与客户端签名

### 安全头信息

| 头名称 | 说明 | 示例 |
|--------|------|------|
| `x-signature` | 请求签名 | `a1b2c3d4e5f6...` |
| `x-timestamp` | 请求时间戳 | `1640995200000` |
| `x-nonce` | 随机数 | `abc123def456` |

## 防重放攻击

### nonce机制

- **生成**：客户端为每个请求生成唯一的随机字符串
- **缓存**：服务端将使用过的nonce存储在Redis中
- **验证**：检查nonce是否已存在，存在则拒绝请求
- **清理**：nonce缓存TTL为10分钟，自动清理过期记录

### 时间戳验证

- **容忍时间**：默认5分钟，可配置
- **验证逻辑**：`|当前时间 - 请求时间戳| <= 容忍时间`
- **防护效果**：防止过期请求重放

## 频率限制策略

### 限制配置

| 接口类型 | 时间窗口 | 最大请求数 | 说明 |
|----------|----------|------------|------|
| 金币上报 | 1分钟 | 10次 | 防止频繁刷金币 |
| 耗时挑战上报 | 1分钟 | 5次 | 防止刷榜行为 |
| 排行榜查询 | 1分钟 | 30次 | 平衡性能与体验 |
| 排行榜刷新 | 1小时 | 3次 | 防止恶意刷新 |

### 滑动窗口算法

```javascript
// 伪代码
function checkRateLimit(key, config) {
  const now = Date.now();
  const windowStart = now - config.windowMs;
  
  // 获取时间窗口内的请求记录
  const requests = getRequestsInWindow(key, windowStart, now);
  
  if (requests.length >= config.maxRequests) {
    return { allowed: false, remaining: 0 };
  }
  
  // 记录本次请求
  recordRequest(key, now);
  return { allowed: true, remaining: config.maxRequests - requests.length - 1 };
}
```

## 数据验证规则

### 金币数据验证

```javascript
// 验证规则
const coinsValidation = {
  range: [0, 999999999999],           // 金币范围
  maxIncrease: 1000000,               // 单次最大增长
  maxDecrease: -10000,                // 最大减少量
  suspiciousThreshold: {
    tooSmall: 10,                     // 过小值（可疑）
    tooLarge: 100000000               // 过大值（高风险）
  }
};
```

### 耗时挑战验证

```javascript
// 最小完成时间（防止不可能的快速完成）
const minCompletionTime = {
  1000: 3000,        // 1K金币最少3秒
  10000: 15000,      // 1万金币最少15秒
  100000: 60000,     // 10万金币最少1分钟
  1000000: 300000,   // 100万金币最少5分钟
  100000000: 1800000 // 1亿金币最少30分钟
};

// 性能提升合理性检查
const maxImprovementRatio = 0.5; // 最大50%性能提升
```

## 客户端集成指南

### 1. 安装依赖

```bash
npm install crypto
```

### 2. 实现签名客户端

```javascript
const SecureApiClient = require('./secure-api-client');

// 初始化客户端（密钥必须与服务端一致）
const client = new SecureApiClient('your-secret-key');

// 上报金币数据
await client.reportCoins(playerId, username, avatar, coins);

// 上报耗时挑战数据
await client.reportTimeChallenge(playerId, username, avatar, timeMs, targetCoins);

// 查询排行榜
const leaderboard = await client.getLeaderboard('coins', 10);
```

### 3. 错误处理

```javascript
try {
  const result = await client.reportCoins(1001, 'Player1', null, 5000);
  console.log('上报成功:', result.data);
} catch (error) {
  if (error.status === 401) {
    console.error('签名验证失败，请检查密钥和签名算法');
  } else if (error.status === 429) {
    console.error('请求过于频繁，请稍后重试');
  } else if (error.status === 400) {
    console.error('数据验证失败:', error.data.errors);
  }
}
```

## 服务端配置

### 环境变量

```bash
# 安全配置
API_SECRET_KEY=your-super-secret-key-change-in-production-environment
NONCE_TTL=600

# 数据库和Redis配置
DB_HOST=localhost
DB_PORT=3306
REDIS_HOST=localhost
REDIS_PORT=6379
```

### 密钥管理最佳实践

1. **生产环境**：使用强随机密钥（至少32字符）
2. **密钥轮换**：定期更换密钥（建议每季度）
3. **环境隔离**：不同环境使用不同密钥
4. **安全存储**：使用密钥管理服务，避免硬编码

## 监控与告警

### 安全事件监控

1. **签名验证失败**：
   - 监控指标：失败率、失败IP分布
   - 告警阈值：1分钟内失败超过10次

2. **频率限制触发**：
   - 监控指标：被限制的IP和用户
   - 告警阈值：单IP 1小时内被限制超过5次

3. **异常数据检测**：
   - 监控指标：高风险数据提交次数
   - 告警阈值：1小时内高风险数据超过3次

### 日志格式

```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "level": "WARN",
  "message": "安全检查失败: 请求签名验证失败",
  "context": {
    "ip": "*************",
    "userAgent": "Mozilla/5.0...",
    "path": "/api/stats/coins/report",
    "method": "POST",
    "playerId": 1001,
    "reason": "invalid_signature"
  }
}
```

## 性能优化

### 缓存策略

1. **nonce缓存**：使用Redis存储，TTL自动清理
2. **频率限制缓存**：滑动窗口数据存储在Redis
3. **签名计算**：避免重复计算，合理使用缓存

### 性能指标

- **签名验证**：< 5ms
- **nonce检查**：< 2ms
- **频率限制检查**：< 3ms
- **数据验证**：< 1ms

## 故障排除

### 常见问题

1. **签名验证失败**
   - 检查密钥是否一致
   - 确认时间戳格式正确
   - 验证数据排序算法

2. **频率限制误触发**
   - 检查时间窗口配置
   - 确认限制键生成逻辑
   - 验证Redis连接状态

3. **nonce重复错误**
   - 确保nonce生成算法随机性
   - 检查系统时钟同步
   - 验证Redis TTL配置

### 调试工具

提供调试脚本用于测试签名生成：

```bash
node scripts/debug-signature.js --data '{"id":1001,"coins":5000}' --timestamp 1640995200000 --nonce abc123
```

## 安全更新

### 版本历史

- **v1.0.0**：基础安全框架
- **v1.1.0**：增加数据验证
- **v1.2.0**：优化频率限制算法

### 升级指南

升级安全组件时需要注意：
1. 向后兼容性
2. 客户端同步更新
3. 密钥迁移策略

## 部署检查清单

### 生产环境部署前检查

- [ ] 更换默认密钥为强随机密钥
- [ ] 配置适当的频率限制参数
- [ ] 启用安全日志记录
- [ ] 配置监控告警
- [ ] 测试所有安全防护功能
- [ ] 验证客户端签名实现
- [ ] 检查Redis连接和TTL配置
- [ ] 确认数据验证规则合理性

### 运维检查清单

- [ ] 定期检查安全日志
- [ ] 监控异常请求模式
- [ ] 定期更新密钥
- [ ] 备份安全配置
- [ ] 测试故障恢复流程

---

**注意**：本文档包含敏感的安全实现细节，请妥善保管，避免泄露给潜在攻击者。
