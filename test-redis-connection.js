#!/usr/bin/env node

/**
 * Redis连接测试脚本
 * 测试ioredis 5.3.0和Keyv适配器的连接
 */

const Redis = require('ioredis');
const { Keyv } = require('keyv');
const KeyvRedis = require('@keyv/redis');

async function testRedisConnection() {
  console.log('🔍 开始测试Redis连接...\n');

  try {
    // 测试1: 直接使用ioredis连接
    console.log('📡 测试1: 直接ioredis连接');
    const redis = new Redis({
      host: '***************',
      port: 6379,
      password: '123456',
      db: 0,
      retryStrategy: (times) => {
        const delay = Math.min(times * 50, 2000);
        return delay;
      },
    });

    // 测试基本操作
    await redis.set('test:ioredis', 'Hello ioredis!', 'EX', 60);
    const value = await redis.get('test:ioredis');
    console.log('✅ ioredis连接成功');
    console.log('写入和读取测试:', value);
    
    // 获取Redis信息
    const info = await redis.info('server');
    const version = info.match(/redis_version:([^\r\n]+)/)?.[1];
    console.log('Redis版本:', version);
    console.log('');

    // 测试2: 使用Keyv + ioredis适配器
    console.log('🔗 测试2: Keyv + ioredis适配器');
    const keyvRedis = new KeyvRedis(redis);
    const keyv = new Keyv({
      store: keyvRedis,
      namespace: 'rankserver',
    });

    // 测试Keyv操作
    await keyv.set('test:keyv', { message: 'Hello Keyv!', timestamp: Date.now() }, 60000);
    const keyvValue = await keyv.get('test:keyv');
    console.log('✅ Keyv适配器连接成功');
    console.log('Keyv读取测试:', JSON.stringify(keyvValue));
    console.log('');

    // 测试3: 检查Redis中的实际键
    console.log('🔍 测试3: 检查Redis中的键');
    const keys = await redis.keys('*');
    console.log('Redis中的所有键:');
    keys.forEach(key => {
      console.log(`  - ${key}`);
    });
    console.log('');

    // 测试4: 检查命名空间键
    console.log('🏷️ 测试4: 检查命名空间键');
    const namespaceKeys = await redis.keys('rankserver:*');
    console.log('命名空间键 (rankserver:*):');
    namespaceKeys.forEach(key => {
      console.log(`  - ${key}`);
    });
    console.log('');

    // 测试5: 性能测试
    console.log('⚡ 测试5: 性能测试');
    const startTime = Date.now();
    const promises = [];
    for (let i = 0; i < 100; i++) {
      promises.push(keyv.set(`perf:test:${i}`, { index: i, data: 'performance test' }, 10000));
    }
    await Promise.all(promises);
    
    const readPromises = [];
    for (let i = 0; i < 100; i++) {
      readPromises.push(keyv.get(`perf:test:${i}`));
    }
    const results = await Promise.all(readPromises);
    const endTime = Date.now();
    
    console.log(`✅ 性能测试完成: 200次操作耗时 ${endTime - startTime}ms`);
    console.log(`成功读取 ${results.filter(r => r !== undefined).length} 条记录`);
    console.log('');

    // 清理测试数据
    console.log('🗑️ 清理测试数据...');
    await redis.del('test:ioredis');
    await keyv.delete('test:keyv');
    
    // 清理性能测试数据
    const perfKeys = await redis.keys('rankserver:perf:test:*');
    if (perfKeys.length > 0) {
      await redis.del(...perfKeys);
    }
    
    console.log('✅ 测试数据清理完成');
    console.log('');

    // 关闭连接
    await redis.quit();
    console.log('🎉 所有测试完成！Redis连接正常工作！');

  } catch (error) {
    console.error('❌ Redis连接测试失败:', error.message);
    if (error.code) {
      console.error('错误代码:', error.code);
    }
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
    process.exit(1);
  }
}

if (require.main === module) {
  testRedisConnection();
}

module.exports = testRedisConnection;
