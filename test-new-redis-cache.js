#!/usr/bin/env node

/**
 * 测试新的Redis缓存方案
 * 验证基于ioredis的直接Redis缓存
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/app.module');
const { RedisCacheService } = require('./dist/common/cache/redis-cache.service');
const { LeaderboardService } = require('./dist/modules/leaderboard/leaderboard.service');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

async function testNewRedisCache() {
  console.log('🔍 测试新的Redis缓存方案...\n');

  let app;
  try {
    // 创建应用实例
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: ['error'],
    });

    const redisCacheService = app.get(RedisCacheService);
    const leaderboardService = app.get(LeaderboardService);

    // 测试1: Redis连接健康检查
    console.log('🏥 测试1: Redis连接健康检查');
    const isHealthy = await redisCacheService.healthCheck();
    console.log(`Redis健康状态: ${isHealthy ? '✅ 正常' : '❌ 异常'}`);
    
    if (isHealthy) {
      const info = await redisCacheService.getInfo();
      console.log(`Redis信息: ${info.host}:${info.port} (DB:${info.db}, 键数:${info.keyCount})`);
    }
    console.log('');

    // 测试2: 基础缓存操作
    console.log('📝 测试2: 基础缓存操作');
    const testKey = 'test:basic-cache';
    const testData = {
      message: 'Hello New Redis Cache!',
      timestamp: Date.now(),
      data: [1, 2, 3, 4, 5]
    };

    // 写入缓存
    await redisCacheService.set(testKey, testData, 60);
    console.log('✅ 缓存写入成功');

    // 读取缓存
    const cachedData = await redisCacheService.get(testKey);
    if (cachedData && JSON.stringify(cachedData) === JSON.stringify(testData)) {
      console.log('✅ 缓存读取成功，数据一致');
    } else {
      console.log('❌ 缓存读取失败或数据不一致');
    }

    // 检查TTL
    const ttl = await redisCacheService.ttl(testKey);
    console.log(`缓存TTL: ${ttl}秒`);

    // 删除缓存
    await redisCacheService.del(testKey);
    const deletedData = await redisCacheService.get(testKey);
    console.log(`删除后读取: ${deletedData === null ? '✅ 已删除' : '❌ 仍存在'}`);
    console.log('');

    // 测试3: 清除旧缓存并检查Redis
    console.log('🗑️ 测试3: 清除旧缓存');
    await redisCacheService.clear('leaderboard:*');
    console.log('✅ 旧排行榜缓存已清除');

    // 检查Redis中的键
    await checkRedisKeys('清除后');
    console.log('');

    // 测试4: 生成排行榜并检查缓存
    console.log('📊 测试4: 生成排行榜并检查缓存');
    await leaderboardService.generateLeaderboard('coins');
    console.log('✅ coins排行榜生成完成');

    // 立即检查Redis中的键
    await checkRedisKeys('生成后');

    // 检查缓存内容
    const coinsCache = await redisCacheService.get('leaderboard:coins');
    if (coinsCache) {
      console.log('✅ coins排行榜缓存存在');
      console.log(`  - 排行榜条目: ${coinsCache.leaderboard?.length || 0}`);
      console.log(`  - 总玩家数: ${coinsCache.totalPlayers || 0}`);
      console.log(`  - 生成时间: ${coinsCache.generatedAt}`);
      
      if (coinsCache.leaderboard && coinsCache.leaderboard.length > 0) {
        const first = coinsCache.leaderboard[0];
        console.log(`  - 第一名: ${first.username} (${first.displayText})`);
      }
    } else {
      console.log('❌ coins排行榜缓存不存在');
    }
    console.log('');

    // 测试5: 通过服务读取排行榜
    console.log('📖 测试5: 通过服务读取排行榜');
    const result = await leaderboardService.getLeaderboard('coins', 3);
    console.log(`读取结果: ${result.fromCache ? '✅ 来自缓存' : '❌ 来自数据库'}`);
    console.log(`排行榜条目: ${result.leaderboard.length}`);
    console.log(`总玩家数: ${result.totalPlayers}`);
    console.log('');

    // 测试6: 生成所有排行榜
    console.log('🚀 测试6: 生成所有排行榜');
    await leaderboardService.generateAllLeaderboards();
    console.log('✅ 所有排行榜生成完成');

    // 检查所有排行榜缓存
    const allKeys = await redisCacheService.keys('leaderboard:*');
    console.log(`生成的排行榜缓存: ${allKeys.length} 个`);
    allKeys.forEach(key => {
      console.log(`  - ${key}`);
    });
    console.log('');

    // 测试7: 验证缓存一致性
    console.log('🔍 测试7: 验证缓存一致性');
    const testTypes = ['coins', 'time_challenge_1000', 'time_challenge_10000'];
    
    for (const type of testTypes) {
      const cached = await redisCacheService.get(`leaderboard:${type}`);
      const fromService = await leaderboardService.getLeaderboard(type, 10);
      
      if (cached && fromService.fromCache) {
        console.log(`✅ ${type}: 缓存一致`);
      } else {
        console.log(`❌ ${type}: 缓存不一致`);
      }
    }
    console.log('');

    // 测试8: 最终Redis状态
    console.log('🔍 测试8: 最终Redis状态');
    await checkRedisKeys('最终');

    console.log('🎉 新Redis缓存方案测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

async function checkRedisKeys(stage) {
  try {
    console.log(`📊 ${stage}Redis状态:`);
    
    // 检查rankserver键
    const { stdout: rankserverKeys } = await execAsync('redis-cli -h 192.168.200.200 -p 6379 -a 123456 keys "rankserver:*"');
    const keyList = rankserverKeys.trim().split('\n').map(line => line.trim()).filter(line => line && line !== '(empty list or set)');
    
    console.log(`  - rankserver:* 键数量: ${keyList.length}`);
    
    if (keyList.length > 0) {
      const leaderboardKeys = keyList.filter(key => key.includes('leaderboard'));
      console.log(`  - 排行榜键数量: ${leaderboardKeys.length}`);
      
      if (leaderboardKeys.length > 0) {
        console.log('  - 排行榜键列表:');
        leaderboardKeys.forEach(key => {
          console.log(`    * ${key}`);
        });
      }
    }
    
  } catch (error) {
    console.log(`  - Redis检查失败: ${error.message}`);
  }
}

if (require.main === module) {
  testNewRedisCache();
}

module.exports = testNewRedisCache;
