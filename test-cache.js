#!/usr/bin/env node

/**
 * 缓存测试脚本
 * 测试Redis缓存是否正常工作
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/app.module');
const { CACHE_MANAGER } = require('@nestjs/cache-manager');

async function testCache() {
  console.log('🔍 开始测试缓存功能...\n');

  let app;
  try {
    // 创建应用实例
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: ['error'],
    });

    const cacheManager = app.get(CACHE_MANAGER);

    // 测试1: 写入缓存
    console.log('📝 测试1: 写入缓存');
    const testKey = 'test:cache:key';
    const testValue = { message: 'Hello Cache!', timestamp: Date.now() };
    
    await cacheManager.set(testKey, testValue, 60000); // 60秒TTL
    console.log('✅ 缓存写入成功');
    console.log('键:', testKey);
    console.log('值:', JSON.stringify(testValue));
    console.log('');

    // 测试2: 读取缓存
    console.log('📖 测试2: 读取缓存');
    const cachedValue = await cacheManager.get(testKey);
    
    if (cachedValue) {
      console.log('✅ 缓存读取成功');
      console.log('读取的值:', JSON.stringify(cachedValue));
    } else {
      console.log('❌ 缓存读取失败，值为空');
    }
    console.log('');

    // 测试3: 测试排行榜缓存键
    console.log('📊 测试3: 测试排行榜缓存');
    const leaderboardKey = 'leaderboard:coins';
    const leaderboardData = {
      type: 'coins',
      leaderboard: [
        { rank: 1, id: 1001, username: 'TestPlayer', value: 50000 }
      ],
      generatedAt: new Date().toISOString(),
      totalPlayers: 1
    };

    await cacheManager.set(leaderboardKey, leaderboardData, 86400000); // 24小时TTL
    console.log('✅ 排行榜缓存写入成功');
    console.log('键:', leaderboardKey);
    console.log('');

    // 测试4: 读取排行榜缓存
    const cachedLeaderboard = await cacheManager.get(leaderboardKey);
    if (cachedLeaderboard) {
      console.log('✅ 排行榜缓存读取成功');
      console.log('排行榜数据:', JSON.stringify(cachedLeaderboard, null, 2));
    } else {
      console.log('❌ 排行榜缓存读取失败');
    }
    console.log('');

    // 测试5: 删除测试缓存
    console.log('🗑️ 测试5: 清理测试缓存');
    await cacheManager.del(testKey);
    await cacheManager.del(leaderboardKey);
    console.log('✅ 测试缓存清理完成');

    console.log('🎉 缓存功能测试完成！');

  } catch (error) {
    console.error('❌ 缓存测试失败:', error.message);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

if (require.main === module) {
  testCache();
}

module.exports = testCache;
