const http = require('http');
const crypto = require('crypto');

// 安全API客户端类
class SecureApiClient {
  constructor(secretKey) {
    this.secretKey = secretKey;
    this.algorithm = 'sha256';
  }

  generateNonce(length = 16) {
    return crypto.randomBytes(length).toString('hex');
  }

  sortObjectKeys(obj) {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sortObjectKeys(item));
    }

    const sortedKeys = Object.keys(obj).sort();
    const sortedObj = {};

    for (const key of sortedKeys) {
      sortedObj[key] = this.sortObjectKeys(obj[key]);
    }

    return sortedObj;
  }

  generateSignature(data, timestamp, nonce) {
    const sortedData = this.sortObjectKeys(data);
    const signString = `${timestamp}${nonce}${JSON.stringify(sortedData)}${this.secretKey}`;
    return crypto.createHash(this.algorithm).update(signString).digest('hex');
  }

  async sendSecureRequest(path, method, data = null, requireSignature = true) {
    const timestamp = Date.now();
    const nonce = this.generateNonce();

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    // 如果需要签名验证，添加安全头
    if (requireSignature && data) {
      const signature = this.generateSignature(data, timestamp, nonce);
      options.headers['x-signature'] = signature;
      options.headers['x-timestamp'] = timestamp.toString();
      options.headers['x-nonce'] = nonce;
    }

    return new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let body = '';
        res.on('data', (chunk) => {
          body += chunk;
        });
        res.on('end', () => {
          try {
            const result = JSON.parse(body);
            resolve({
              status: res.statusCode,
              headers: res.headers,
              data: result
            });
          } catch (e) {
            resolve({
              status: res.statusCode,
              headers: res.headers,
              data: body
            });
          }
        });
      });

      req.on('error', (e) => {
        reject(e);
      });

      if (data) {
        req.write(JSON.stringify(data));
      }
      req.end();
    });
  }
}

// 测试数据 - 不同挑战目标的耗时数据
const challengeTestData = [
  // 1K金币挑战
  { id: 4001, username: "Speed1K_1", avatar: "https://example.com/speed1.jpg", timeMs: 5500, targetCoins: 1000 },
  { id: 4002, username: "Speed1K_2", avatar: "https://example.com/speed2.jpg", timeMs: 6200, targetCoins: 1000 },
  { id: 4003, username: "Speed1K_3", avatar: "https://example.com/speed3.jpg", timeMs: 7100, targetCoins: 1000 },
  
  // 1万金币挑战
  { id: 4001, username: "Speed1K_1", avatar: "https://example.com/speed1.jpg", timeMs: 45000, targetCoins: 10000 },
  { id: 4004, username: "Speed10K_1", avatar: "https://example.com/speed4.jpg", timeMs: 38000, targetCoins: 10000 },
  { id: 4005, username: "Speed10K_2", avatar: "https://example.com/speed5.jpg", timeMs: 52000, targetCoins: 10000 },
  
  // 10万金币挑战
  { id: 4004, username: "Speed10K_1", avatar: "https://example.com/speed4.jpg", timeMs: 180000, targetCoins: 100000 },
  { id: 4006, username: "Speed100K_1", avatar: "https://example.com/speed6.jpg", timeMs: 165000, targetCoins: 100000 },
  
  // 100万金币挑战
  { id: 4007, username: "Speed1M_1", avatar: "https://example.com/speed7.jpg", timeMs: 900000, targetCoins: 1000000 },
];

// 初始化安全客户端
const secureClient = new SecureApiClient('your-super-secret-key-change-in-production-environment');

// 测试函数
async function testGetChallengeTargets() {
  return secureClient.sendSecureRequest('/api/leaderboard/challenges', 'GET', null, false);
}

async function testReportTimeChallengeStats(challengeData) {
  return secureClient.sendSecureRequest('/api/stats/time-challenge/report', 'POST', challengeData, true);
}

async function testGetChallengeLeaderboard(challengeType) {
  return secureClient.sendSecureRequest(`/api/leaderboard/${challengeType}?limit=10`, 'GET', null, false);
}

async function testGetLeaderboardTypes() {
  return secureClient.sendSecureRequest('/api/leaderboard/types', 'GET', null, false);
}

// 运行测试
async function runChallengeTests() {
  console.log('🚀 开始测试挑战排行榜功能...\n');

  try {
    // 测试 1: 获取挑战目标
    console.log('🎯 测试 1: 获取挑战目标');
    const challengeTargetsResult = await testGetChallengeTargets();
    console.log(`状态码: ${challengeTargetsResult.status}`);
    console.log('响应:', JSON.stringify(challengeTargetsResult.data, null, 2));
    console.log('');

    // 测试 2: 获取排行榜类型（包含挑战类型）
    console.log('📋 测试 2: 获取排行榜类型');
    const typesResult = await testGetLeaderboardTypes();
    console.log(`状态码: ${typesResult.status}`);
    console.log('响应:', JSON.stringify(typesResult.data, null, 2));
    console.log('');

    // 测试 3: 批量上报挑战数据
    console.log('📊 测试 3: 批量上报挑战数据');
    for (let i = 0; i < challengeTestData.length; i++) {
      const data = challengeTestData[i];
      console.log(`上报第${i+1}条数据: ${data.username} - ${data.targetCoins}金币挑战 - ${data.timeMs}ms`);
      
      const result = await testReportTimeChallengeStats(data);
      if (result.status !== 200) {
        console.log(`❌ 上报失败:`, result.data);
      } else {
        console.log(`✅ 上报成功: ${result.data.message}`);
      }
      
      // 稍微延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    console.log('');

    // 测试 4: 刷新所有排行榜（注意：这个接口已移除，改为使用脚本）
    console.log('🔄 测试 4: 跳过排行榜刷新（已改为服务端脚本）');
    console.log('提示: 请使用 node scripts/refresh-leaderboards.js 来刷新排行榜');
    console.log('');

    // 等待排行榜生成（由于没有刷新，这里等待时间缩短）
    console.log('⏳ 等待系统处理...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 测试 5: 获取各种挑战排行榜
    const challengeTypes = ['time_challenge_1000', 'time_challenge_10000', 'time_challenge_100000', 'time_challenge_1000000'];
    
    for (const challengeType of challengeTypes) {
      console.log(`🏆 测试: 获取 ${challengeType} 排行榜`);
      const leaderboardResult = await testGetChallengeLeaderboard(challengeType);
      console.log(`状态码: ${leaderboardResult.status}`);
      
      if (leaderboardResult.status === 200 && leaderboardResult.data.success) {
        const leaderboard = leaderboardResult.data.data.leaderboard;
        console.log(`排行榜名称: ${leaderboardResult.data.data.type}`);
        console.log(`总玩家数: ${leaderboardResult.data.data.totalPlayers}`);
        console.log('前3名:');
        leaderboard.slice(0, 3).forEach(player => {
          console.log(`  ${player.rank}. ${player.username} - ${player.displayText}`);
        });
      } else {
        console.log('响应:', JSON.stringify(leaderboardResult.data, null, 2));
      }
      console.log('');
    }

    console.log('✅ 所有挑战排行榜测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

runChallengeTests();
