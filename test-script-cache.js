#!/usr/bin/env node

/**
 * 测试脚本缓存行为
 * 验证脚本执行时是否正确更新Redis缓存
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/app.module');
const { LeaderboardService } = require('./dist/modules/leaderboard/leaderboard.service');
const { CACHE_MANAGER } = require('@nestjs/cache-manager');

async function testScriptCache() {
  console.log('🔍 测试脚本缓存行为...\n');

  let app;
  try {
    // 创建应用实例
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: ['error'],
    });

    const leaderboardService = app.get(LeaderboardService);
    const cacheManager = app.get(CACHE_MANAGER);

    // 测试1: 清除现有缓存
    console.log('🗑️ 测试1: 清除现有缓存');
    await leaderboardService.clearCache('coins');
    console.log('✅ coins缓存已清除');
    console.log('');

    // 测试2: 检查缓存是否真的被清除
    console.log('📖 测试2: 检查缓存是否被清除');
    const cacheKey = 'leaderboard:coins';
    const cachedData = await cacheManager.get(cacheKey);
    console.log('缓存数据:', cachedData ? '存在' : '不存在');
    console.log('');

    return;

    // 测试3: 生成排行榜（应该更新缓存）
    console.log('📊 测试3: 生成coins排行榜');
    await leaderboardService.generateLeaderboard('coins');
    console.log('✅ coins排行榜生成完成');
    console.log('');

    // 测试4: 立即检查缓存是否被更新
    console.log('🔍 测试4: 检查缓存是否被更新');
    const newCachedData = await cacheManager.get(cacheKey);
    if (newCachedData) {
      console.log('✅ 缓存已更新');
      const data = JSON.parse(newCachedData);
      console.log('缓存数据预览:');
      console.log(`  - 排行榜条目数: ${data.leaderboard.length}`);
      console.log(`  - 总玩家数: ${data.totalPlayers}`);
      console.log(`  - 生成时间: ${data.generatedAt}`);
      console.log(`  - 第一名: ${data.leaderboard[0]?.username} (${data.leaderboard[0]?.displayText})`);
    } else {
      console.log('❌ 缓存未更新');
    }
    console.log('');

    // 测试5: 通过服务读取排行榜（应该从缓存获取）
    console.log('📖 测试5: 通过服务读取排行榜');
    const result = await leaderboardService.getLeaderboard('coins', 5);
    console.log('读取结果:');
    console.log(`  - 来源: ${result.fromCache ? '缓存' : '数据库'}`);
    console.log(`  - 排行榜条目数: ${result.leaderboard.length}`);
    console.log(`  - 总玩家数: ${result.totalPlayers}`);
    console.log('');

    // 测试6: 生成所有排行榜
    console.log('🚀 测试6: 生成所有排行榜');
    await leaderboardService.generateAllLeaderboards();
    console.log('✅ 所有排行榜生成完成');
    console.log('');

    // 测试7: 检查多个排行榜的缓存状态
    console.log('🔍 测试7: 检查多个排行榜的缓存状态');
    const testTypes = ['coins', 'time_challenge_1000', 'time_challenge_10000'];
    
    for (const type of testTypes) {
      const key = `leaderboard:${type}`;
      const cached = await cacheManager.get(key);
      if (cached) {
        const data = JSON.parse(cached);
        console.log(`✅ ${type}: 缓存存在 (${data.leaderboard.length}条记录)`);
      } else {
        console.log(`❌ ${type}: 缓存不存在`);
      }
    }
    console.log('');

    // 测试8: 验证缓存数据的完整性
    console.log('🔍 测试8: 验证缓存数据完整性');
    const coinsCache = await cacheManager.get('leaderboard:coins');
    if (coinsCache) {
      const data = JSON.parse(coinsCache);
      console.log('coins缓存数据结构:');
      console.log(`  - leaderboard: ${Array.isArray(data.leaderboard) ? '数组' : '非数组'}`);
      console.log(`  - generatedAt: ${data.generatedAt ? '存在' : '不存在'}`);
      console.log(`  - totalPlayers: ${typeof data.totalPlayers === 'number' ? '数字' : '非数字'}`);
      
      if (data.leaderboard.length > 0) {
        const first = data.leaderboard[0];
        console.log('  - 第一条记录结构:');
        console.log(`    - rank: ${first.rank}`);
        console.log(`    - id: ${first.id}`);
        console.log(`    - username: ${first.username}`);
        console.log(`    - value: ${first.value}`);
        console.log(`    - displayText: ${first.displayText}`);
      }
    }

    console.log('\n🎉 脚本缓存测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

if (require.main === module) {
  testScriptCache();
}

module.exports = testScriptCache;
