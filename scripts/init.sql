-- 排行榜服务器数据库初始化脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS rank_server 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE rank_server;

-- 创建玩家表
CREATE TABLE IF NOT EXISTS players (
    id BIGINT PRIMARY KEY COMMENT '玩家ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    avatar VARCHAR(255) NULL COMMENT '头像URL',
    coins BIGINT DEFAULT 0 COMMENT '金币数量',
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_coins (coins DESC),
    INDEX idx_updated_at (updatedAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='玩家数据表';

-- 创建排行榜缓存表
CREATE TABLE IF NOT EXISTS leaderboard_cache (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    rankPosition INT NOT NULL COMMENT '排名位置',
    playerId BIGINT NOT NULL COMMENT '玩家ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    avatar VARCHAR(255) NULL COMMENT '头像URL',
    coins BIGINT NOT NULL COMMENT '金币数量',
    generatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间',
    
    INDEX idx_rank (rankPosition),
    INDEX idx_generated_at (generatedAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排行榜缓存表';

-- 插入测试数据（可选）
INSERT IGNORE INTO players (id, username, avatar, coins) VALUES
(1001, 'TestPlayer1', 'https://example.com/avatar1.jpg', 50000),
(1002, 'TestPlayer2', 'https://example.com/avatar2.jpg', 45000),
(1003, 'TestPlayer3', 'https://example.com/avatar3.jpg', 40000),
(1004, 'TestPlayer4', 'https://example.com/avatar4.jpg', 35000),
(1005, 'TestPlayer5', 'https://example.com/avatar5.jpg', 30000);

-- 创建用户并授权（如果需要）
-- CREATE USER IF NOT EXISTS 'nestjs'@'%' IDENTIFIED BY 'nestjs123';
-- GRANT ALL PRIVILEGES ON rank_server.* TO 'nestjs'@'%';
-- FLUSH PRIVILEGES;
