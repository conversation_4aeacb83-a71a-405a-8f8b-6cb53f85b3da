#!/usr/bin/env node

/**
 * 排行榜刷新脚本
 * 用于服务端手动刷新排行榜数据
 * 
 * 使用方法:
 * node scripts/refresh-leaderboards.js                    # 刷新所有排行榜
 * node scripts/refresh-leaderboards.js --type coins       # 刷新指定类型排行榜
 * node scripts/refresh-leaderboards.js --help             # 显示帮助信息
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');
const { LeaderboardService } = require('../dist/modules/leaderboard/leaderboard.service');
const { 
  getAllChallengeTargets, 
  generateChallengeLeaderboardType,
  LeaderboardType 
} = require('../dist/common/enums/leaderboard-type.enum');

// 命令行参数解析
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    type: null,
    help: false,
    verbose: false,
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case '--type':
      case '-t':
        options.type = args[++i];
        break;
      case '--help':
      case '-h':
        options.help = true;
        break;
      case '--verbose':
      case '-v':
        options.verbose = true;
        break;
      default:
        console.error(`未知参数: ${arg}`);
        process.exit(1);
    }
  }

  return options;
}

// 显示帮助信息
function showHelp() {
  console.log(`
排行榜刷新脚本

使用方法:
  node scripts/refresh-leaderboards.js [选项]

选项:
  -t, --type <type>    刷新指定类型的排行榜
  -v, --verbose        显示详细输出
  -h, --help           显示此帮助信息

排行榜类型:
  coins                金币排行榜
  time_challenge_1000  1K金币挑战排行榜
  time_challenge_10000 1万金币挑战排行榜
  time_challenge_100000 10万金币挑战排行榜
  time_challenge_1000000 100万金币挑战排行榜
  time_challenge_100000000 1亿金币挑战排行榜

示例:
  node scripts/refresh-leaderboards.js
  node scripts/refresh-leaderboards.js --type coins
  node scripts/refresh-leaderboards.js --type time_challenge_1000 --verbose
`);
}

// 获取所有可用的排行榜类型
function getAllLeaderboardTypes() {
  const baseTypes = [LeaderboardType.COINS];
  const challengeTargets = getAllChallengeTargets();
  const challengeTypes = challengeTargets.map(target => 
    generateChallengeLeaderboardType(target.coins)
  );
  
  return [...baseTypes, ...challengeTypes];
}

// 验证排行榜类型
function validateLeaderboardType(type) {
  const validTypes = getAllLeaderboardTypes();
  return validTypes.includes(type);
}

// 格式化时间
function formatDuration(ms) {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  return `${(ms / 60000).toFixed(1)}min`;
}

// 主函数
async function main() {
  const options = parseArgs();

  if (options.help) {
    showHelp();
    return;
  }

  console.log('🚀 排行榜刷新脚本启动...\n');

  let app;
  try {
    // 创建NestJS应用实例
    if (options.verbose) {
      console.log('📦 正在初始化应用...');
    }
    
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: options.verbose ? ['log', 'error', 'warn'] : ['error'],
    });

    const leaderboardService = app.get(LeaderboardService);

    if (options.type) {
      // 刷新指定类型的排行榜
      if (!validateLeaderboardType(options.type)) {
        console.error(`❌ 错误: 不支持的排行榜类型 "${options.type}"`);
        console.log('\n可用的排行榜类型:');
        getAllLeaderboardTypes().forEach(type => {
          console.log(`  - ${type}`);
        });
        process.exit(1);
      }

      console.log(`🔄 开始刷新 ${options.type} 排行榜...`);
      const startTime = Date.now();

      await leaderboardService.generateLeaderboard(options.type);

      const duration = Date.now() - startTime;
      console.log(`✅ ${options.type} 排行榜刷新完成! 耗时: ${formatDuration(duration)}`);

    } else {
      // 刷新所有排行榜
      console.log('🔄 开始刷新所有排行榜...');
      const startTime = Date.now();

      const allTypes = getAllLeaderboardTypes();
      console.log(`📊 发现 ${allTypes.length} 种排行榜类型:`);
      
      if (options.verbose) {
        allTypes.forEach(type => {
          console.log(`  - ${type}`);
        });
        console.log('');
      }

      let successCount = 0;
      let failureCount = 0;

      for (const type of allTypes) {
        try {
          if (options.verbose) {
            console.log(`🔄 正在刷新 ${type}...`);
          }
          
          const typeStartTime = Date.now();
          await leaderboardService.generateLeaderboard(type);
          const typeDuration = Date.now() - typeStartTime;
          
          successCount++;
          
          if (options.verbose) {
            console.log(`✅ ${type} 完成 (${formatDuration(typeDuration)})`);
          } else {
            process.stdout.write('.');
          }
        } catch (error) {
          failureCount++;
          console.error(`❌ ${type} 失败: ${error.message}`);
        }
      }

      if (!options.verbose) {
        console.log(''); // 换行
      }

      const totalDuration = Date.now() - startTime;
      console.log(`\n📊 刷新完成统计:`);
      console.log(`  ✅ 成功: ${successCount} 个`);
      console.log(`  ❌ 失败: ${failureCount} 个`);
      console.log(`  ⏱️  总耗时: ${formatDuration(totalDuration)}`);

      if (failureCount > 0) {
        console.log(`\n⚠️  有 ${failureCount} 个排行榜刷新失败，请检查日志`);
        process.exit(1);
      } else {
        console.log(`\n🎉 所有排行榜刷新成功!`);
      }
    }

  } catch (error) {
    console.error('❌ 脚本执行失败:', error.message);
    if (options.verbose) {
      console.error(error.stack);
    }
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

// 处理中断信号
process.on('SIGINT', () => {
  console.log('\n👋 收到中断信号，正在退出...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 收到终止信号，正在退出...');
  process.exit(0);
});

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main, getAllLeaderboardTypes, validateLeaderboardType };
