#!/usr/bin/env node

/**
 * 快速排行榜刷新脚本
 * 简化版本，用于快速刷新所有排行榜
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../dist/app.module');
const { LeaderboardService } = require('../dist/modules/leaderboard/leaderboard.service');

async function quickRefresh() {
  console.log('🚀 快速刷新所有排行榜...');
  
  let app;
  try {
    // 创建应用实例（静默模式）
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: ['error'],
    });

    const leaderboardService = app.get(LeaderboardService);
    
    const startTime = Date.now();
    await leaderboardService.generateAllLeaderboards();
    const duration = Date.now() - startTime;
    
    console.log(`✅ 所有排行榜刷新完成! 耗时: ${Math.round(duration / 1000)}s`);
    
  } catch (error) {
    console.error('❌ 刷新失败:', error.message);
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

if (require.main === module) {
  quickRefresh();
}

module.exports = quickRefresh;
