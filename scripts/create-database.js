const mysql = require('mysql2/promise');
require('dotenv').config();

async function createDatabase() {
  let connection, dbConnection;

  connection = await mysql.createConnection({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
  });

  try {
    // 创建数据库
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${process.env.DB_DATABASE}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log(`✅ 数据库 '${process.env.DB_DATABASE}' 创建成功！`);
    
    // 重新连接到新创建的数据库
    await connection.end();
    dbConnection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
    });
    
    // 创建玩家表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS players (
        id BIGINT PRIMARY KEY COMMENT '玩家ID',
        username VARCHAR(50) NOT NULL COMMENT '用户名',
        avatar VARCHAR(255) NULL COMMENT '头像URL',
        coins BIGINT DEFAULT 0 COMMENT '金币数量',
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        
        INDEX idx_coins (coins DESC),
        INDEX idx_updated_at (updatedAt)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='玩家数据表'
    `);
    console.log('✅ 玩家表创建成功！');

    // 创建排行榜缓存表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS leaderboard_cache (
        id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
        rankPosition INT NOT NULL COMMENT '排名位置',
        playerId BIGINT NOT NULL COMMENT '玩家ID',
        username VARCHAR(50) NOT NULL COMMENT '用户名',
        avatar VARCHAR(255) NULL COMMENT '头像URL',
        coins BIGINT NOT NULL COMMENT '金币数量',
        generatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间',
        
        INDEX idx_rank (rankPosition),
        INDEX idx_generated_at (generatedAt)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排行榜缓存表'
    `);
    console.log('✅ 排行榜缓存表创建成功！');

    // 插入测试数据
    await dbConnection.execute(`
      INSERT IGNORE INTO players (id, username, avatar, coins) VALUES
      (1001, 'TestPlayer1', 'https://example.com/avatar1.jpg', 50000),
      (1002, 'TestPlayer2', 'https://example.com/avatar2.jpg', 45000),
      (1003, 'TestPlayer3', 'https://example.com/avatar3.jpg', 40000),
      (1004, 'TestPlayer4', 'https://example.com/avatar4.jpg', 35000),
      (1005, 'TestPlayer5', 'https://example.com/avatar5.jpg', 30000)
    `);
    console.log('✅ 测试数据插入成功！');

  } catch (error) {
    console.error('❌ 数据库创建失败:', error.message);
    process.exit(1);
  } finally {
    if (connection && !connection.destroyed) await connection.end();
    if (dbConnection && !dbConnection.destroyed) await dbConnection.end();
  }
}

createDatabase();
