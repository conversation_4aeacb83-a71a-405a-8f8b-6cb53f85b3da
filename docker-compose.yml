version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: rank_server_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: rank_server
      MYSQL_USER: nestjs
      MYSQL_PASSWORD: nestjs123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: rank_server_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  # NestJS 应用 (开发环境)
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: rank_server_app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USERNAME: nestjs
      DB_PASSWORD: nestjs123
      DB_DATABASE: rank_server
      REDIS_HOST: redis
      REDIS_PORT: 6379
      NODE_ENV: development
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - mysql
      - redis
    command: npm run start:dev

volumes:
  mysql_data:
  redis_data:
