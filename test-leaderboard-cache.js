#!/usr/bin/env node

/**
 * 排行榜缓存测试脚本
 * 测试排行榜服务的缓存功能
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/app.module');
const { LeaderboardService } = require('./dist/modules/leaderboard/leaderboard.service');

async function testLeaderboardCache() {
  console.log('🔍 开始测试排行榜缓存功能...\n');

  let app;
  try {
    // 创建应用实例
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: ['error'],
    });

    const leaderboardService = app.get(LeaderboardService);

    // 测试1: 生成排行榜并检查缓存
    console.log('📊 测试1: 生成coins排行榜');
    await leaderboardService.generateLeaderboard('coins');
    console.log('✅ coins排行榜生成完成');
    console.log('');

    // 测试2: 从缓存获取排行榜
    console.log('📖 测试2: 从缓存获取排行榜');
    const cachedResult = await leaderboardService.getLeaderboard('coins', 10);
    
    if (cachedResult && cachedResult.fromCache) {
      console.log('✅ 成功从缓存获取排行榜');
      console.log('缓存数据:', JSON.stringify(cachedResult, null, 2));
    } else {
      console.log('❌ 未能从缓存获取排行榜');
      console.log('结果:', JSON.stringify(cachedResult, null, 2));
    }
    console.log('');

    // 测试3: 清除缓存并重新获取
    console.log('🗑️ 测试3: 清除缓存');
    await leaderboardService.clearCache('coins');
    console.log('✅ 缓存已清除');
    
    const afterClearResult = await leaderboardService.getLeaderboard('coins', 10);
    if (afterClearResult && !afterClearResult.fromCache) {
      console.log('✅ 清除缓存后从数据库获取成功');
    } else {
      console.log('❌ 清除缓存后仍从缓存获取');
    }
    console.log('');

    console.log('🎉 排行榜缓存功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

if (require.main === module) {
  testLeaderboardCache();
}

module.exports = testLeaderboardCache;
