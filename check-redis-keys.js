#!/usr/bin/env node

/**
 * Redis键检查脚本
 * 检查Redis中的所有键，特别是排行榜相关的键
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/app.module');
const { CACHE_MANAGER } = require('@nestjs/cache-manager');

async function checkRedisKeys() {
  console.log('🔍 检查Redis键...\n');

  let app;
  try {
    // 创建应用实例
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: ['error'],
    });

    const cacheManager = app.get(CACHE_MANAGER);

    // 测试写入一个键
    console.log('📝 写入测试键...');
    await cacheManager.set('test:check:key', { test: 'value' }, 60000);
    console.log('✅ 测试键写入成功');

    // 读取测试键
    const testValue = await cacheManager.get('test:check:key');
    console.log('📖 读取测试键:', JSON.stringify(testValue));

    // 写入排行榜键
    console.log('\n📊 写入排行榜测试键...');
    const leaderboardKey = 'leaderboard:test';
    const leaderboardData = {
      type: 'test',
      leaderboard: [{ rank: 1, id: 1001, username: 'Test', value: 100 }],
      generatedAt: new Date(),
      totalPlayers: 1
    };

    await cacheManager.set(leaderboardKey, leaderboardData, 86400000);
    console.log('✅ 排行榜测试键写入成功');

    // 读取排行榜键
    const leaderboardValue = await cacheManager.get(leaderboardKey);
    console.log('📖 读取排行榜测试键:', JSON.stringify(leaderboardValue, null, 2));

    // 清理测试键
    await cacheManager.del('test:check:key');
    await cacheManager.del(leaderboardKey);
    console.log('\n🗑️ 测试键已清理');

    console.log('\n🎉 Redis键检查完成！');

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

if (require.main === module) {
  checkRedisKeys();
}

module.exports = checkRedisKeys;
