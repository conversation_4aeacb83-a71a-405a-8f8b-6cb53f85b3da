const crypto = require('crypto');
const http = require('http');

/**
 * 客户端签名示例
 * 演示如何正确生成签名并发送安全请求
 */
class SecureApiClient {
  constructor(secretKey) {
    this.secretKey = secretKey;
    this.algorithm = 'sha256';
  }

  /**
   * 生成随机nonce
   */
  generateNonce(length = 16) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 对象键名排序
   */
  sortObjectKeys(obj) {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sortObjectKeys(item));
    }

    const sortedKeys = Object.keys(obj).sort();
    const sortedObj = {};
    
    for (const key of sortedKeys) {
      sortedObj[key] = this.sortObjectKeys(obj[key]);
    }
    
    return sortedObj;
  }

  /**
   * 生成数据签名
   */
  generateSignature(data, timestamp, nonce) {
    const sortedData = this.sortObjectKeys(data);
    const signString = `${timestamp}${nonce}${JSON.stringify(sortedData)}${this.secretKey}`;
    return crypto.createHash(this.algorithm).update(signString).digest('hex');
  }

  /**
   * 发送安全请求
   */
  async sendSecureRequest(path, method, data = null) {
    const timestamp = Date.now();
    const nonce = this.generateNonce();
    const signature = this.generateSignature(data || {}, timestamp, nonce);

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'x-signature': signature,
        'x-timestamp': timestamp.toString(),
        'x-nonce': nonce,
      }
    };

    return new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let body = '';
        res.on('data', (chunk) => {
          body += chunk;
        });
        res.on('end', () => {
          try {
            const result = JSON.parse(body);
            resolve({ 
              status: res.statusCode, 
              headers: res.headers,
              data: result 
            });
          } catch (e) {
            resolve({ 
              status: res.statusCode, 
              headers: res.headers,
              data: body 
            });
          }
        });
      });

      req.on('error', (e) => {
        reject(e);
      });

      if (data) {
        req.write(JSON.stringify(data));
      }
      req.end();
    });
  }

  /**
   * 上报金币数据
   */
  async reportCoins(playerId, username, avatar, coins) {
    const data = {
      id: playerId,
      username: username,
      avatar: avatar,
      coins: coins
    };

    return this.sendSecureRequest('/api/stats/coins/report', 'POST', data);
  }

  /**
   * 上报耗时挑战数据
   */
  async reportTimeChallenge(playerId, username, avatar, timeMs, targetCoins) {
    const data = {
      id: playerId,
      username: username,
      avatar: avatar,
      timeMs: timeMs,
      targetCoins: targetCoins
    };

    return this.sendSecureRequest('/api/stats/time-challenge/report', 'POST', data);
  }

  /**
   * 获取排行榜
   */
  async getLeaderboard(type, limit = 10) {
    return this.sendSecureRequest(`/api/leaderboard/${type}?limit=${limit}`, 'GET');
  }
}

// 使用示例
async function testSecureApi() {
  // 注意：这里的密钥必须与服务器端配置的一致
  const client = new SecureApiClient('your-super-secret-key-change-in-production-environment');

  console.log('🔐 开始测试安全API...\n');

  try {
    // 测试1: 上报金币数据
    console.log('💰 测试1: 上报金币数据');
    const coinsResult = await client.reportCoins(
      5001, 
      'SecurePlayer1', 
      'https://example.com/secure1.jpg', 
      88888
    );
    console.log(`状态码: ${coinsResult.status}`);
    console.log('响应:', JSON.stringify(coinsResult.data, null, 2));
    console.log('');

    // 测试2: 上报耗时挑战数据
    console.log('⏱️ 测试2: 上报耗时挑战数据');
    const timeResult = await client.reportTimeChallenge(
      5002, 
      'SecurePlayer2', 
      'https://example.com/secure2.jpg', 
      4200, 
      1000
    );
    console.log(`状态码: ${timeResult.status}`);
    console.log('响应:', JSON.stringify(timeResult.data, null, 2));
    console.log('');

    // 等待一下
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 测试3: 获取排行榜
    console.log('🏆 测试3: 获取金币排行榜');
    const leaderboardResult = await client.getLeaderboard('coins', 5);
    console.log(`状态码: ${leaderboardResult.status}`);
    console.log('频率限制剩余:', leaderboardResult.headers['x-ratelimit-remaining']);
    console.log('响应:', JSON.stringify(leaderboardResult.data, null, 2));
    console.log('');

    console.log('✅ 安全API测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testSecureApi();
}

module.exports = SecureApiClient;
