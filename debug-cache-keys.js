#!/usr/bin/env node

/**
 * 缓存键调试脚本
 * 调试缓存键的实际存储情况
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/app.module');
const { CACHE_MANAGER } = require('@nestjs/cache-manager');
const { LeaderboardService } = require('./dist/modules/leaderboard/leaderboard.service');

async function debugCacheKeys() {
  console.log('🔍 调试缓存键存储...\n');

  let app;
  try {
    // 创建应用实例
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: ['error'],
    });

    const cacheManager = app.get(CACHE_MANAGER);
    const leaderboardService = app.get(LeaderboardService);

    // 测试1: 直接写入缓存并立即检查
    console.log('📝 测试1: 直接写入缓存');
    const testKey = 'debug:test:key';
    const testValue = { message: 'Debug test', timestamp: Date.now() };
    
    await cacheManager.set(testKey, testValue, 300000); // 5分钟TTL
    console.log('✅ 缓存写入完成');
    console.log('键:', testKey);
    console.log('值:', JSON.stringify(testValue));
    
    // 立即读取
    const readValue = await cacheManager.get(testKey);
    console.log('读取结果:', JSON.stringify(readValue));
    console.log('');

    // 测试2: 生成排行榜并立即检查缓存
    console.log('📊 测试2: 生成排行榜并检查缓存');
    await leaderboardService.generateLeaderboard('coins');
    console.log('✅ coins排行榜生成完成');
    
    // 尝试直接读取缓存
    const coinsCache = await cacheManager.get('leaderboard:coins');
    console.log('直接读取leaderboard:coins:', coinsCache ? '有数据' : '无数据');
    
    // 通过服务读取
    const coinsResult = await leaderboardService.getLeaderboard('coins', 5);
    console.log('通过服务读取:', coinsResult.fromCache ? '来自缓存' : '来自数据库');
    console.log('');

    // 测试3: 尝试不同的键格式
    console.log('🔑 测试3: 尝试不同的键格式');
    const possibleKeys = [
      'leaderboard:coins',
      'cache:leaderboard:coins',
      'nestjs:leaderboard:coins',
      'keyv:leaderboard:coins',
    ];
    
    for (const key of possibleKeys) {
      const value = await cacheManager.get(key);
      console.log(`${key}: ${value ? '有数据' : '无数据'}`);
    }
    console.log('');

    // 测试4: 写入后立即检查Redis
    console.log('🔍 测试4: 写入后立即检查Redis');
    const redisTestKey = 'redis:direct:test';
    await cacheManager.set(redisTestKey, { test: 'redis direct' }, 60000);
    console.log('✅ Redis测试键写入完成');
    
    // 清理测试键
    await cacheManager.del(testKey);
    await cacheManager.del(redisTestKey);
    console.log('🗑️ 测试键已清理');

    console.log('🎉 缓存键调试完成！');

  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

if (require.main === module) {
  debugCacheKeys();
}

module.exports = debugCacheKeys;
