const http = require('http');

// 测试数据
const coinsTestData = {
  id: 3001,
  username: "CoinsPlayer",
  avatar: "https://example.com/coins-avatar.jpg",
  coins: 75000
};

const timeChallengeTestData = {
  id: 3002,
  username: "SpeedPlayer",
  avatar: "https://example.com/speed-avatar.jpg",
  timeMs: 8500,
  targetCoins: 1000
};

// HTTP 请求工具函数
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// 测试函数
async function testCoinsStatsReport() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/stats/coins/report',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  return makeRequest(options, coinsTestData);
}

async function testTimeChallengeStatsReport() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/stats/time-challenge/report',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  return makeRequest(options, timeChallengeTestData);
}

async function testGetLeaderboardTypes() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/leaderboard/types',
    method: 'GET'
  };

  return makeRequest(options);
}

async function testGetCoinsLeaderboard() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/leaderboard/coins?limit=10',
    method: 'GET'
  };

  return makeRequest(options);
}

async function testGetTimeChallengeLeaderboard() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/leaderboard/time_challenge?limit=10',
    method: 'GET'
  };

  return makeRequest(options);
}

async function testRefreshCoinsLeaderboard() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/leaderboard/coins/refresh',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  return makeRequest(options);
}

async function testRefreshTimeChallengeLeaderboard() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/leaderboard/time_challenge/refresh',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  return makeRequest(options);
}

async function testRefreshAllLeaderboards() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/leaderboard/refresh-all',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  return makeRequest(options);
}

// 运行测试
async function runOptimizedTests() {
  console.log('🚀 开始测试优化后的排行榜服务器架构...\n');

  try {
    // 测试 1: 获取排行榜类型
    console.log('📋 测试 1: 获取排行榜类型');
    const typesResult = await testGetLeaderboardTypes();
    console.log(`状态码: ${typesResult.status}`);
    console.log('响应:', JSON.stringify(typesResult.data, null, 2));
    console.log('');

    // 测试 2: 上报金币统计数据
    console.log('💰 测试 2: 上报金币统计数据');
    const coinsResult = await testCoinsStatsReport();
    console.log(`状态码: ${coinsResult.status}`);
    console.log('响应:', JSON.stringify(coinsResult.data, null, 2));
    console.log('');

    // 测试 3: 上报耗时挑战统计数据
    console.log('⏱️ 测试 3: 上报耗时挑战统计数据');
    const timeResult = await testTimeChallengeStatsReport();
    console.log(`状态码: ${timeResult.status}`);
    console.log('响应:', JSON.stringify(timeResult.data, null, 2));
    console.log('');

    // 测试 4: 刷新所有排行榜
    console.log('🔄 测试 4: 刷新所有排行榜');
    const refreshAllResult = await testRefreshAllLeaderboards();
    console.log(`状态码: ${refreshAllResult.status}`);
    console.log('响应:', JSON.stringify(refreshAllResult.data, null, 2));
    console.log('');

    // 等待排行榜生成
    console.log('⏳ 等待排行榜生成...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 测试 5: 获取金币排行榜
    console.log('🏆 测试 5: 获取金币排行榜');
    const coinsLeaderboardResult = await testGetCoinsLeaderboard();
    console.log(`状态码: ${coinsLeaderboardResult.status}`);
    console.log('响应:', JSON.stringify(coinsLeaderboardResult.data, null, 2));
    console.log('');

    // 测试 6: 获取耗时挑战排行榜
    console.log('🏃 测试 6: 获取耗时挑战排行榜');
    const timeLeaderboardResult = await testGetTimeChallengeLeaderboard();
    console.log(`状态码: ${timeLeaderboardResult.status}`);
    console.log('响应:', JSON.stringify(timeLeaderboardResult.data, null, 2));
    console.log('');

    console.log('✅ 所有优化架构测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

runOptimizedTests();
