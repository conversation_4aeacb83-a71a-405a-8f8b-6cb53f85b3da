# 数据库配置
DB_HOST=***************
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=mysql_kKE5zT
DB_DATABASE=rank_server

# Redis 配置
REDIS_HOST=***************
REDIS_PORT=6379
REDIS_PASSWORD=123456

# 应用配置
PORT=3000
NODE_ENV=development

# 排行榜配置
LEADERBOARD_SIZE=100
LEADERBOARD_CACHE_TTL=86400

# 定时任务配置 (Cron 表达式)
# 每日凌晨0点更新排行榜
LEADERBOARD_UPDATE_CRON=0 0 0 * * *

# 安全配置
API_SECRET_KEY=your-super-secret-key-change-in-production-environment
NONCE_TTL=600
