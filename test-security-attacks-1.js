const http = require('http');

// HTTP 请求工具函数
function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const result = JSON.parse(body);
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: result
                    });
                } catch (e) {
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: body
                    });
                }
            });
        });

        req.on('error', (e) => {
            reject(e);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

// 测试恶意攻击场景
async function testSecurityAttacks() {
    console.log('🛡️ 开始测试安全防护能力...\n');

    try {
        // 攻击1: 无签名的数据伪造攻击
        console.log('⚠️ 攻击1: 尝试无签名上报虚假金币数据');
        const fakeCoinsResult = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/stats/coins/report',
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        }, {
            id: 99999,
            username: "HackerPlayer",
            coins: 999999999999 // 尝试上报超大金币数量
        });

        console.log(`状态码: ${fakeCoinsResult.status}`);
        console.log('防护结果:', fakeCoinsResult.data.message);
        console.log('');

        // 攻击2: 异常耗时数据攻击
        console.log('⚠️ 攻击2: 尝试上报不可能的超快耗时');
        const fakeTimeResult = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/stats/time-challenge/report',
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        }, {
            id: 99998,
            username: "CheatPlayer",
            timeMs: 100, // 100毫秒完成1000金币挑战（不可能）
            targetCoins: 1000
        });

        console.log(`状态码: ${fakeTimeResult.status}`);
        console.log('防护结果:', fakeTimeResult.data.message);
        console.log('');

        // 攻击3: 频率限制测试
        console.log('⚠️ 攻击3: 尝试频繁请求攻击');
        const promises = [];
        for (let i = 0; i < 35; i++) { // 超过30次限制
            promises.push(makeRequest({
                hostname: 'localhost',
                port: 3000,
                path: '/api/leaderboard/coins',
                method: 'GET'
            }));
        }

        const results = await Promise.all(promises);
        const successCount = results.filter(r => r.status === 200).length;
        const blockedCount = results.filter(r => r.status === 429).length;

        console.log(`成功请求: ${successCount}次`);
        console.log(`被阻止请求: ${blockedCount}次`);

        if (blockedCount > 0) {
            console.log('频率限制防护生效！');
            console.log('最后一次被阻止的响应:', results[results.length - 1].data);
        }
        console.log('');

        // 攻击4: 恶意数据格式攻击
        console.log('⚠️ 攻击4: 尝试发送恶意数据格式');
        const maliciousResult = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/stats/coins/report',
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        }, {
            id: "malicious_string", // 错误的数据类型
            username: null,
            coins: -999999 // 负数金币
        });

        console.log(`状态码: ${maliciousResult.status}`);
        console.log('防护结果:', maliciousResult.data.message);
        if (maliciousResult.data.errors) {
            console.log('检测到的问题:', maliciousResult.data.errors);
        }
        console.log('');

        console.log('✅ 安全防护测试完成！');
        console.log('📊 测试总结:');
        console.log('- 签名验证: ✅ 有效阻止未签名请求');
        console.log('- 数据验证: ✅ 有效检测异常数据');
        console.log('- 频率限制: ✅ 有效防止频繁攻击');
        console.log('- 格式验证: ✅ 有效拒绝恶意格式');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

testSecurityAttacks();
